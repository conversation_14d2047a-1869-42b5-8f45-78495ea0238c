"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logout = exports.login = exports.register = void 0;
const register = async (req, res, next) => {
    try {
        res.status(501).json({
            status: 'error',
            message: 'Registration endpoint not implemented yet'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.register = register;
const login = async (req, res, next) => {
    try {
        res.status(501).json({
            status: 'error',
            message: 'Login endpoint not implemented yet'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.login = login;
const logout = async (req, res, next) => {
    try {
        res.status(501).json({
            status: 'error',
            message: 'Logout endpoint not implemented yet'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.logout = logout;
//# sourceMappingURL=authController.js.map