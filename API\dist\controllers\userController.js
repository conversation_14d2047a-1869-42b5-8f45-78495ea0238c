"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllUsers = exports.updateProfile = exports.getProfile = void 0;
const getProfile = async (req, res, next) => {
    try {
        res.status(501).json({
            status: 'error',
            message: 'Get profile endpoint not implemented yet'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getProfile = getProfile;
const updateProfile = async (req, res, next) => {
    try {
        res.status(501).json({
            status: 'error',
            message: 'Update profile endpoint not implemented yet'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateProfile = updateProfile;
const getAllUsers = async (req, res, next) => {
    try {
        res.status(501).json({
            status: 'error',
            message: 'Get all users endpoint not implemented yet'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getAllUsers = getAllUsers;
//# sourceMappingURL=userController.js.map