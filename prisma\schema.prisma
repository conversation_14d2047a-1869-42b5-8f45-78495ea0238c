// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User model for authentication and profile management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String?
  lastName  String?
  role      UserRole @default(ALUMNI)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Profile information
  profile UserProfile?

  @@map("users")
}

// User profile with additional information
model UserProfile {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Personal information
  phone        String?
  dateOfBirth  DateTime?
  bio          String?   @db.Text
  profileImage String?

  // Professional information
  company  String?
  position String?
  industry String?

  // Education information
  graduationYear Int?
  degree         String?
  major          String?

  // Social links
  linkedinUrl String?
  githubUrl   String?
  websiteUrl  String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("user_profiles")
}

// Enum for user roles
enum UserRole {
  ADMIN
  ALUMNI
  STUDENT
  FACULTY
}
