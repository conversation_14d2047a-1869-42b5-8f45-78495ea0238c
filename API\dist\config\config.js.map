{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/config/config.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAG5B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAgDhB,MAAM,MAAM,GAAW;IAErB,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,EAAE,EAAE,CAAC;IAC9C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;IAC9C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB;IAGhE,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE;IAG3C,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,qBAAqB;IAC1D,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,IAAI;IAChD,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,yBAAyB;IAC7E,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,KAAK;IAGhE,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,IAAI,EAAE,EAAE,CAAC;IAGtE,IAAI,EAAE;QACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,gBAAgB;QAC/C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,KAAK,EAAE,EAAE,CAAC;QAClD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;QACjC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;QACjC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,uBAAuB;QAC5D,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,WAAW;KAC/C;IAGD,MAAM,EAAE;QACN,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,UAAU,EAAE,EAAE,CAAC;QAClE,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;KAClD;IAGD,SAAS,EAAE;QACT,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,EAAE,EAAE,CAAC;QACpE,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,KAAK,EAAE,EAAE,CAAC;KACxE;IAGD,IAAI,EAAE;QACJ,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC;KACrF;CACF,CAAC;AAYO,wBAAM;AATf,MAAM,eAAe,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,oBAAoB,CAAC,CAAC;AAE7E,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;IACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;QACzB,OAAO,CAAC,KAAK,CAAC,4CAA4C,MAAM,EAAE,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC"}