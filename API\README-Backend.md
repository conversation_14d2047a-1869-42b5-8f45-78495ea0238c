# IonAlumni Backend

A TypeScript Express.js backend application with Prisma ORM and MySQL database for the IonAlumni platform.

## 🚀 Features

- **TypeScript** - Type-safe development
- **Express.js** - Fast, unopinionated web framework
- **Prisma ORM** - Modern database toolkit
- **MySQL** - Reliable relational database
- **JWT Authentication** - Secure token-based auth
- **Security Middleware** - Helmet, CORS, Rate limiting
- **Environment Configuration** - Centralized config management
- **Development Tools** - Nodemon, TypeScript compilation

## 📁 Project Structure

```
src/
├── config/          # Configuration files
│   ├── config.ts    # Environment configuration
│   └── database.ts  # Database connection
├── controllers/     # Route controllers
│   ├── authController.ts
│   └── userController.ts
├── middleware/      # Custom middleware
├── models/          # Data models (if needed)
├── routes/          # API routes
├── services/        # Business logic
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
├── app.ts           # Express app configuration
└── server.ts        # Server entry point

prisma/
└── schema.prisma    # Database schema
```

## 🛠️ Setup Instructions

### Prerequisites

- Node.js (v18 or higher)
- MySQL (v8.0 or higher)
- npm or yarn

### Installation

1. **Navigate to the API directory:**
   ```bash
   cd API
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your database credentials and other configuration.

4. **Set up the database:**
   ```bash
   # Generate Prisma client
   npm run db:generate

   # Push the schema to database (for development)
   npm run db:push

   # Or run migrations (for production)
   npm run db:migrate
   ```

5. **Start the development server:**
   ```bash
   npm run dev
   ```

## 📝 Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build the application for production
- `npm run start` - Start production server
- `npm run start:prod` - Start production server with NODE_ENV=production
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema changes to database
- `npm run db:migrate` - Run database migrations
- `npm run db:reset` - Reset database and run migrations
- `npm run db:studio` - Open Prisma Studio
- `npm run type-check` - Run TypeScript type checking

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example` for full list):

- `DATABASE_URL` - MySQL connection string
- `JWT_SECRET` - Secret key for JWT tokens
- `PORT` - Server port (default: 5000)
- `NODE_ENV` - Environment (development/production)

### Database Schema

The application includes a basic user management schema with:

- **User** model - Basic user information and authentication
- **UserProfile** model - Extended user profile information
- **UserRole** enum - User roles (ADMIN, ALUMNI, STUDENT, FACULTY)

## 🔐 Security Features

- **Helmet** - Security headers
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - Request rate limiting
- **Input Validation** - Request validation middleware
- **JWT Authentication** - Secure token-based authentication
- **Password Hashing** - Bcrypt password hashing

## 🚦 API Endpoints

### Health Check
- `GET /health` - Server health status

### API Info
- `GET /api/v1` - API information and available endpoints

### Authentication (To be implemented)
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout

### Users (To be implemented)
- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile
- `GET /api/v1/users` - Get all users (admin only)

## 🧪 Development

### Adding New Features

1. **Controllers** - Add business logic in `src/controllers/`
2. **Routes** - Define API endpoints in `src/routes/`
3. **Middleware** - Add custom middleware in `src/middleware/`
4. **Services** - Add business services in `src/services/`
5. **Types** - Define TypeScript types in `src/types/`

### Database Changes

1. Update `prisma/schema.prisma`
2. Run `npm run db:migrate` to create migration
3. Run `npm run db:generate` to update Prisma client

## 📚 Next Steps

1. Implement authentication logic
2. Add user management endpoints
3. Set up email service
4. Add file upload functionality
5. Implement logging system
6. Add unit and integration tests
7. Set up API documentation (Swagger)
8. Add data validation schemas

## 🤝 Contributing

1. Follow TypeScript best practices
2. Use meaningful commit messages
3. Add proper error handling
4. Include type definitions
5. Update documentation as needed
