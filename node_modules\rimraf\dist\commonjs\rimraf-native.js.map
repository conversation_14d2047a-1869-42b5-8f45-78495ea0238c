{"version": 3, "file": "<PERSON><PERSON>f-native.js", "sourceRoot": "", "sources": ["../../src/rimraf-native.ts"], "names": [], "mappings": ";;;AACA,mCAA0C;AAC1C,MAAM,EAAE,EAAE,EAAE,GAAG,gBAAQ,CAAA;AAEhB,MAAM,YAAY,GAAG,KAAK,EAC/B,IAAY,EACZ,GAAuB,EACL,EAAE;IACpB,MAAM,EAAE,CAAC,IAAI,EAAE;QACb,GAAG,GAAG;QACN,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI;KAChB,CAAC,CAAA;IACF,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAVY,QAAA,YAAY,gBAUxB;AAEM,MAAM,gBAAgB,GAAG,CAC9B,IAAY,EACZ,GAAsB,EACb,EAAE;IACX,IAAA,cAAM,EAAC,IAAI,EAAE;QACX,GAAG,GAAG;QACN,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI;KAChB,CAAC,CAAA;IACF,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAVY,QAAA,gBAAgB,oBAU5B", "sourcesContent": ["import { RimrafAsyncOptions, RimrafSyncOptions } from './index.js'\nimport { promises, rmSync } from './fs.js'\nconst { rm } = promises\n\nexport const rimrafNative = async (\n  path: string,\n  opt: RimrafAsyncOptions,\n): Promise<boolean> => {\n  await rm(path, {\n    ...opt,\n    force: true,\n    recursive: true,\n  })\n  return true\n}\n\nexport const rimrafNativeSync = (\n  path: string,\n  opt: RimrafSyncOptions,\n): boolean => {\n  rmSync(path, {\n    ...opt,\n    force: true,\n    recursive: true,\n  })\n  return true\n}\n"]}