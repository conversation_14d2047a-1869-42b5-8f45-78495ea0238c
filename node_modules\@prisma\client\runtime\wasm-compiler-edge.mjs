var Yc=Object.create;var Hr=Object.defineProperty;var Zc=Object.getOwnPropertyDescriptor;var Xc=Object.getOwnPropertyNames;var eu=Object.getPrototypeOf,tu=Object.prototype.hasOwnProperty;var de=(e,t)=>()=>(e&&(t=e(e=0)),t);var ne=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),qt=(e,t)=>{for(var r in t)Hr(e,r,{get:t[r],enumerable:!0})},Oo=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Xc(t))!tu.call(e,i)&&i!==r&&Hr(e,i,{get:()=>t[i],enumerable:!(n=Zc(t,i))||n.enumerable});return e};var Ae=(e,t,r)=>(r=e!=null?Yc(eu(e)):{},Oo(t||!e||!e.__esModule?Hr(r,"default",{value:e,enumerable:!0}):r,e)),ru=e=>Oo(Hr({},"__esModule",{value:!0}),e);function ri(e,t){if(t=t.toLowerCase(),t==="utf8"||t==="utf-8")return new h(su.encode(e));if(t==="base64"||t==="base64url")return e=e.replace(/-/g,"+").replace(/_/g,"/"),e=e.replace(/[^A-Za-z0-9+/]/g,""),new h([...atob(e)].map(r=>r.charCodeAt(0)));if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return new h([...e].map(r=>r.charCodeAt(0)));if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let r=new h(e.length*2),n=new DataView(r.buffer);for(let i=0;i<e.length;i++)n.setUint16(i*2,e.charCodeAt(i),!0);return r}if(t==="hex"){let r=new h(e.length/2);for(let n=0,i=0;i<e.length;i+=2,n++)r[n]=parseInt(e.slice(i,i+2),16);return r}_o(`encoding "${t}"`)}function nu(e){let r=Object.getOwnPropertyNames(DataView.prototype).filter(a=>a.startsWith("get")||a.startsWith("set")),n=r.map(a=>a.replace("get","read").replace("set","write")),i=(a,f)=>function(w=0){return z(w,"offset"),ue(w,"offset"),X(w,"offset",this.length-1),new DataView(this.buffer)[r[a]](w,f)},o=(a,f)=>function(w,v=0){let A=r[a].match(/set(\w+\d+)/)[1].toLowerCase(),R=ou[A];return z(v,"offset"),ue(v,"offset"),X(v,"offset",this.length-1),iu(w,"value",R[0],R[1]),new DataView(this.buffer)[r[a]](v,w,f),v+parseInt(r[a].match(/\d+/)[0])/8},s=a=>{a.forEach(f=>{f.includes("Uint")&&(e[f.replace("Uint","UInt")]=e[f]),f.includes("Float64")&&(e[f.replace("Float64","Double")]=e[f]),f.includes("Float32")&&(e[f.replace("Float32","Float")]=e[f])})};n.forEach((a,f)=>{a.startsWith("read")&&(e[a]=i(f,!1),e[a+"LE"]=i(f,!0),e[a+"BE"]=i(f,!1)),a.startsWith("write")&&(e[a]=o(f,!1),e[a+"LE"]=o(f,!0),e[a+"BE"]=o(f,!1)),s([a,a+"LE",a+"BE"])})}function _o(e){throw new Error(`Buffer polyfill does not implement "${e}"`)}function Gr(e,t){if(!(e instanceof Uint8Array))throw new TypeError(`The "${t}" argument must be an instance of Buffer or Uint8Array`)}function X(e,t,r=cu+1){if(e<0||e>r){let n=new RangeError(`The value of "${t}" is out of range. It must be >= 0 && <= ${r}. Received ${e}`);throw n.code="ERR_OUT_OF_RANGE",n}}function z(e,t){if(typeof e!="number"){let r=new TypeError(`The "${t}" argument must be of type number. Received type ${typeof e}.`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function ue(e,t){if(!Number.isInteger(e)||Number.isNaN(e)){let r=new RangeError(`The value of "${t}" is out of range. It must be an integer. Received ${e}`);throw r.code="ERR_OUT_OF_RANGE",r}}function iu(e,t,r,n){if(e<r||e>n){let i=new RangeError(`The value of "${t}" is out of range. It must be >= ${r} and <= ${n}. Received ${e}`);throw i.code="ERR_OUT_OF_RANGE",i}}function Do(e,t){if(typeof e!="string"){let r=new TypeError(`The "${t}" argument must be of type string. Received type ${typeof e}`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function uu(e,t="utf8"){return h.from(e,t)}var h,ou,su,au,lu,cu,y,ni,c=de(()=>{"use strict";h=class e extends Uint8Array{_isBuffer=!0;get offset(){return this.byteOffset}static alloc(t,r=0,n="utf8"){return Do(n,"encoding"),e.allocUnsafe(t).fill(r,n)}static allocUnsafe(t){return e.from(t)}static allocUnsafeSlow(t){return e.from(t)}static isBuffer(t){return t&&!!t._isBuffer}static byteLength(t,r="utf8"){if(typeof t=="string")return ri(t,r).byteLength;if(t&&t.byteLength)return t.byteLength;let n=new TypeError('The "string" argument must be of type string or an instance of Buffer or ArrayBuffer.');throw n.code="ERR_INVALID_ARG_TYPE",n}static isEncoding(t){return lu.includes(t)}static compare(t,r){Gr(t,"buff1"),Gr(r,"buff2");for(let n=0;n<t.length;n++){if(t[n]<r[n])return-1;if(t[n]>r[n])return 1}return t.length===r.length?0:t.length>r.length?1:-1}static from(t,r="utf8"){if(t&&typeof t=="object"&&t.type==="Buffer")return new e(t.data);if(typeof t=="number")return new e(new Uint8Array(t));if(typeof t=="string")return ri(t,r);if(ArrayBuffer.isView(t)){let{byteOffset:n,byteLength:i,buffer:o}=t;return"map"in t&&typeof t.map=="function"?new e(t.map(s=>s%256),n,i):new e(o,n,i)}if(t&&typeof t=="object"&&("length"in t||"byteLength"in t||"buffer"in t))return new e(t);throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}static concat(t,r){if(t.length===0)return e.alloc(0);let n=[].concat(...t.map(o=>[...o])),i=e.alloc(r!==void 0?r:n.length);return i.set(r!==void 0?n.slice(0,r):n),i}slice(t=0,r=this.length){return this.subarray(t,r)}subarray(t=0,r=this.length){return Object.setPrototypeOf(super.subarray(t,r),e.prototype)}reverse(){return super.reverse(),this}readIntBE(t,r){z(t,"offset"),ue(t,"offset"),X(t,"offset",this.length-1),z(r,"byteLength"),ue(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return n.getUint8(0)&128&&(i-=Math.pow(256,r)),i}readIntLE(t,r){z(t,"offset"),ue(t,"offset"),X(t,"offset",this.length-1),z(r,"byteLength"),ue(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return n.getUint8(r-1)&128&&(i-=Math.pow(256,r)),i}readUIntBE(t,r){z(t,"offset"),ue(t,"offset"),X(t,"offset",this.length-1),z(r,"byteLength"),ue(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return i}readUintBE(t,r){return this.readUIntBE(t,r)}readUIntLE(t,r){z(t,"offset"),ue(t,"offset"),X(t,"offset",this.length-1),z(r,"byteLength"),ue(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return i}readUintLE(t,r){return this.readUIntLE(t,r)}writeIntBE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntBE(t,r,n)}writeIntLE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntLE(t,r,n)}writeUIntBE(t,r,n){z(r,"offset"),ue(r,"offset"),X(r,"offset",this.length-1),z(n,"byteLength"),ue(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=n-1;o>=0;o--)i.setUint8(o,t&255),t=t/256;return r+n}writeUintBE(t,r,n){return this.writeUIntBE(t,r,n)}writeUIntLE(t,r,n){z(r,"offset"),ue(r,"offset"),X(r,"offset",this.length-1),z(n,"byteLength"),ue(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=0;o<n;o++)i.setUint8(o,t&255),t=t/256;return r+n}writeUintLE(t,r,n){return this.writeUIntLE(t,r,n)}toJSON(){return{type:"Buffer",data:Array.from(this)}}swap16(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=2)t.setUint16(r,t.getUint16(r,!0),!1);return this}swap32(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=4)t.setUint32(r,t.getUint32(r,!0),!1);return this}swap64(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=8)t.setBigUint64(r,t.getBigUint64(r,!0),!1);return this}compare(t,r=0,n=t.length,i=0,o=this.length){return Gr(t,"target"),z(r,"targetStart"),z(n,"targetEnd"),z(i,"sourceStart"),z(o,"sourceEnd"),X(r,"targetStart"),X(n,"targetEnd",t.length),X(i,"sourceStart"),X(o,"sourceEnd",this.length),e.compare(this.slice(i,o),t.slice(r,n))}equals(t){return Gr(t,"otherBuffer"),this.length===t.length&&this.every((r,n)=>r===t[n])}copy(t,r=0,n=0,i=this.length){X(r,"targetStart"),X(n,"sourceStart",this.length),X(i,"sourceEnd"),r>>>=0,n>>>=0,i>>>=0;let o=0;for(;n<i&&!(this[n]===void 0||t[r]===void 0);)t[r]=this[n],o++,n++,r++;return o}write(t,r,n,i="utf8"){let o=typeof r=="string"?0:r??0,s=typeof n=="string"?this.length-o:n??this.length-o;return i=typeof r=="string"?r:typeof n=="string"?n:i,z(o,"offset"),z(s,"length"),X(o,"offset",this.length),X(s,"length",this.length),(i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le")&&(s=s-s%2),ri(t,i).copy(this,o,0,s)}fill(t=0,r=0,n=this.length,i="utf-8"){let o=typeof r=="string"?0:r,s=typeof n=="string"?this.length:n;if(i=typeof r=="string"?r:typeof n=="string"?n:i,t=e.from(typeof t=="number"?[t]:t??[],i),Do(i,"encoding"),X(o,"offset",this.length),X(s,"end",this.length),t.length!==0)for(let a=o;a<s;a+=t.length)super.set(t.slice(0,t.length+a>=this.length?this.length-a:t.length),a);return this}includes(t,r=null,n="utf-8"){return this.indexOf(t,r,n)!==-1}lastIndexOf(t,r=null,n="utf-8"){return this.indexOf(t,r,n,!0)}indexOf(t,r=null,n="utf-8",i=!1){let o=i?this.findLastIndex.bind(this):this.findIndex.bind(this);n=typeof r=="string"?r:n;let s=e.from(typeof t=="number"?[t]:t,n),a=typeof r=="string"?0:r;return a=typeof r=="number"?a:null,a=Number.isNaN(a)?null:a,a??=i?this.length:0,a=a<0?this.length+a:a,s.length===0&&i===!1?a>=this.length?this.length:a:s.length===0&&i===!0?(a>=this.length?this.length:a)||this.length:o((f,w)=>(i?w<=a:w>=a)&&this[w]===s[0]&&s.every((A,R)=>this[w+R]===A))}toString(t="utf8",r=0,n=this.length){if(r=r<0?0:r,t=t.toString().toLowerCase(),n<=0)return"";if(t==="utf8"||t==="utf-8")return au.decode(this.slice(r,n));if(t==="base64"||t==="base64url"){let i=btoa(this.reduce((o,s)=>o+ni(s),""));return t==="base64url"?i.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):i}if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return this.slice(r,n).reduce((i,o)=>i+ni(o&(t==="ascii"?127:255)),"");if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let i=new DataView(this.buffer.slice(r,n));return Array.from({length:i.byteLength/2},(o,s)=>s*2+1<i.byteLength?ni(i.getUint16(s*2,!0)):"").join("")}if(t==="hex")return this.slice(r,n).reduce((i,o)=>i+o.toString(16).padStart(2,"0"),"");_o(`encoding "${t}"`)}toLocaleString(){return this.toString()}inspect(){return`<Buffer ${this.toString("hex").match(/.{1,2}/g).join(" ")}>`}};ou={int8:[-128,127],int16:[-32768,32767],int32:[-2147483648,2147483647],uint8:[0,255],uint16:[0,65535],uint32:[0,4294967295],float32:[-1/0,1/0],float64:[-1/0,1/0],bigint64:[-0x8000000000000000n,0x7fffffffffffffffn],biguint64:[0n,0xffffffffffffffffn]},su=new TextEncoder,au=new TextDecoder,lu=["utf8","utf-8","hex","base64","ascii","binary","base64url","ucs2","ucs-2","utf16le","utf-16le","latin1","latin-1"],cu=4294967295;nu(h.prototype);y=new Proxy(uu,{construct(e,[t,r]){return h.from(t,r)},get(e,t){return h[t]}}),ni=String.fromCodePoint});var g,u=de(()=>{"use strict";g={nextTick:(e,...t)=>{setTimeout(()=>{e(...t)},0)},env:{},version:"",cwd:()=>"/",stderr:{},argv:["/bin/node"],pid:1e4}});var b,p=de(()=>{"use strict";b=globalThis.performance??(()=>{let e=Date.now();return{now:()=>Date.now()-e}})()});var E,m=de(()=>{"use strict";E=()=>{};E.prototype=E});var d=de(()=>{"use strict"});function Uo(e,t){var r,n,i,o,s,a,f,w,v=e.constructor,A=v.precision;if(!e.s||!t.s)return t.s||(t=new v(e)),G?V(t,A):t;if(f=e.d,w=t.d,s=e.e,i=t.e,f=f.slice(),o=s-i,o){for(o<0?(n=f,o=-o,a=w.length):(n=w,i=s,a=f.length),s=Math.ceil(A/j),a=s>a?s+1:a+1,o>a&&(o=a,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for(a=f.length,o=w.length,a-o<0&&(o=a,n=w,w=f,f=n),r=0;o;)r=(f[--o]=f[o]+w[o]+r)/ee|0,f[o]%=ee;for(r&&(f.unshift(r),++i),a=f.length;f[--a]==0;)f.pop();return t.d=f,t.e=i,G?V(t,A):t}function Re(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Ke+e)}function Ce(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=j-n.length,r&&(o+=Ve(r)),o+=n;s=e[t],n=s+"",r=j-n.length,r&&(o+=Ve(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function Fo(e,t){var r,n,i,o,s,a,f=0,w=0,v=e.constructor,A=v.precision;if(Y(e)>16)throw Error(oi+Y(e));if(!e.s)return new v(fe);for(t==null?(G=!1,a=A):a=t,s=new v(.03125);e.abs().gte(.1);)e=e.times(s),w+=5;for(n=Math.log(Je(2,w))/Math.LN10*2+5|0,a+=n,r=i=o=new v(fe),v.precision=a;;){if(i=V(i.times(e),a),r=r.times(++f),s=o.plus(Me(i,r,a)),Ce(s.d).slice(0,a)===Ce(o.d).slice(0,a)){for(;w--;)o=V(o.times(o),a);return v.precision=A,t==null?(G=!0,V(o,A)):o}o=s}}function Y(e){for(var t=e.e*j,r=e.d[0];r>=10;r/=10)t++;return t}function ii(e,t,r){if(t>e.LN10.sd())throw G=!0,r&&(e.precision=r),Error(he+"LN10 precision limit exceeded");return V(new e(e.LN10),t)}function Ve(e){for(var t="";e--;)t+="0";return t}function Bt(e,t){var r,n,i,o,s,a,f,w,v,A=1,R=10,C=e,D=C.d,I=C.constructor,M=I.precision;if(C.s<1)throw Error(he+(C.s?"NaN":"-Infinity"));if(C.eq(fe))return new I(0);if(t==null?(G=!1,w=M):w=t,C.eq(10))return t==null&&(G=!0),ii(I,w);if(w+=R,I.precision=w,r=Ce(D),n=r.charAt(0),o=Y(C),Math.abs(o)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)C=C.times(e),r=Ce(C.d),n=r.charAt(0),A++;o=Y(C),n>1?(C=new I("0."+r),o++):C=new I(n+"."+r.slice(1))}else return f=ii(I,w+2,M).times(o+""),C=Bt(new I(n+"."+r.slice(1)),w-R).plus(f),I.precision=M,t==null?(G=!0,V(C,M)):C;for(a=s=C=Me(C.minus(fe),C.plus(fe),w),v=V(C.times(C),w),i=3;;){if(s=V(s.times(v),w),f=a.plus(Me(s,new I(i),w)),Ce(f.d).slice(0,w)===Ce(a.d).slice(0,w))return a=a.times(2),o!==0&&(a=a.plus(ii(I,w+2,M).times(o+""))),a=Me(a,new I(A),w),I.precision=M,t==null?(G=!0,V(a,M)):a;a=f,i+=2}}function Mo(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=pt(r/j),e.d=[],n=(r+1)%j,r<0&&(n+=j),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=j;n<i;)e.d.push(+t.slice(n,n+=j));t=t.slice(n),n=j-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),G&&(e.e>Wr||e.e<-Wr))throw Error(oi+r)}else e.s=0,e.e=0,e.d=[0];return e}function V(e,t,r){var n,i,o,s,a,f,w,v,A=e.d;for(s=1,o=A[0];o>=10;o/=10)s++;if(n=t-s,n<0)n+=j,i=t,w=A[v=0];else{if(v=Math.ceil((n+1)/j),o=A.length,v>=o)return e;for(w=o=A[v],s=1;o>=10;o/=10)s++;n%=j,i=n-j+s}if(r!==void 0&&(o=Je(10,s-i-1),a=w/o%10|0,f=t<0||A[v+1]!==void 0||w%o,f=r<4?(a||f)&&(r==0||r==(e.s<0?3:2)):a>5||a==5&&(r==4||f||r==6&&(n>0?i>0?w/Je(10,s-i):0:A[v-1])%10&1||r==(e.s<0?8:7))),t<1||!A[0])return f?(o=Y(e),A.length=1,t=t-o-1,A[0]=Je(10,(j-t%j)%j),e.e=pt(-t/j)||0):(A.length=1,A[0]=e.e=e.s=0),e;if(n==0?(A.length=v,o=1,v--):(A.length=v+1,o=Je(10,j-n),A[v]=i>0?(w/Je(10,s-i)%Je(10,i)|0)*o:0),f)for(;;)if(v==0){(A[0]+=o)==ee&&(A[0]=1,++e.e);break}else{if(A[v]+=o,A[v]!=ee)break;A[v--]=0,o=1}for(n=A.length;A[--n]===0;)A.pop();if(G&&(e.e>Wr||e.e<-Wr))throw Error(oi+Y(e));return e}function Vo(e,t){var r,n,i,o,s,a,f,w,v,A,R=e.constructor,C=R.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new R(e),G?V(t,C):t;if(f=e.d,A=t.d,n=t.e,w=e.e,f=f.slice(),s=w-n,s){for(v=s<0,v?(r=f,s=-s,a=A.length):(r=A,n=w,a=f.length),i=Math.max(Math.ceil(C/j),a)+2,s>i&&(s=i,r.length=1),r.reverse(),i=s;i--;)r.push(0);r.reverse()}else{for(i=f.length,a=A.length,v=i<a,v&&(a=i),i=0;i<a;i++)if(f[i]!=A[i]){v=f[i]<A[i];break}s=0}for(v&&(r=f,f=A,A=r,t.s=-t.s),a=f.length,i=A.length-a;i>0;--i)f[a++]=0;for(i=A.length;i>s;){if(f[--i]<A[i]){for(o=i;o&&f[--o]===0;)f[o]=ee-1;--f[o],f[i]+=ee}f[i]-=A[i]}for(;f[--a]===0;)f.pop();for(;f[0]===0;f.shift())--n;return f[0]?(t.d=f,t.e=n,G?V(t,C):t):new R(0)}function ze(e,t,r){var n,i=Y(e),o=Ce(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ve(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+Ve(-i-1)+o,r&&(n=r-s)>0&&(o+=Ve(n))):i>=s?(o+=Ve(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Ve(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Ve(n))),e.s<0?"-"+o:o}function Lo(e,t){if(e.length>t)return e.length=t,!0}function $o(e){var t,r,n;function i(o){var s=this;if(!(s instanceof i))return new i(o);if(s.constructor=i,o instanceof i){s.s=o.s,s.e=o.e,s.d=(o=o.d)?o.slice():o;return}if(typeof o=="number"){if(o*0!==0)throw Error(Ke+o);if(o>0)s.s=1;else if(o<0)o=-o,s.s=-1;else{s.s=0,s.e=0,s.d=[0];return}if(o===~~o&&o<1e7){s.e=0,s.d=[o];return}return Mo(s,o.toString())}else if(typeof o!="string")throw Error(Ke+o);if(o.charCodeAt(0)===45?(o=o.slice(1),s.s=-1):s.s=1,mu.test(o))Mo(s,o);else throw Error(Ke+o)}if(i.prototype=S,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=$o,i.config=i.set=du,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function du(e){if(!e||typeof e!="object")throw Error(he+"Object expected");var t,r,n,i=["precision",1,ut,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(pt(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Ke+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Ke+r+": "+n);return this}var ut,pu,si,G,he,Ke,oi,pt,Je,mu,fe,ee,j,No,Wr,S,Me,si,Jr,qo=de(()=>{"use strict";c();u();p();m();d();l();ut=1e9,pu={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},G=!0,he="[DecimalError] ",Ke=he+"Invalid argument: ",oi=he+"Exponent out of range: ",pt=Math.floor,Je=Math.pow,mu=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ee=1e7,j=7,No=9007199254740991,Wr=pt(No/j),S={};S.absoluteValue=S.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};S.comparedTo=S.cmp=function(e){var t,r,n,i,o=this;if(e=new o.constructor(e),o.s!==e.s)return o.s||-e.s;if(o.e!==e.e)return o.e>e.e^o.s<0?1:-1;for(n=o.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(o.d[t]!==e.d[t])return o.d[t]>e.d[t]^o.s<0?1:-1;return n===i?0:n>i^o.s<0?1:-1};S.decimalPlaces=S.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*j;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};S.dividedBy=S.div=function(e){return Me(this,new this.constructor(e))};S.dividedToIntegerBy=S.idiv=function(e){var t=this,r=t.constructor;return V(Me(t,new r(e),0,1),r.precision)};S.equals=S.eq=function(e){return!this.cmp(e)};S.exponent=function(){return Y(this)};S.greaterThan=S.gt=function(e){return this.cmp(e)>0};S.greaterThanOrEqualTo=S.gte=function(e){return this.cmp(e)>=0};S.isInteger=S.isint=function(){return this.e>this.d.length-2};S.isNegative=S.isneg=function(){return this.s<0};S.isPositive=S.ispos=function(){return this.s>0};S.isZero=function(){return this.s===0};S.lessThan=S.lt=function(e){return this.cmp(e)<0};S.lessThanOrEqualTo=S.lte=function(e){return this.cmp(e)<1};S.logarithm=S.log=function(e){var t,r=this,n=r.constructor,i=n.precision,o=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(fe))throw Error(he+"NaN");if(r.s<1)throw Error(he+(r.s?"NaN":"-Infinity"));return r.eq(fe)?new n(0):(G=!1,t=Me(Bt(r,o),Bt(e,o),o),G=!0,V(t,i))};S.minus=S.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Vo(t,e):Uo(t,(e.s=-e.s,e))};S.modulo=S.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(he+"NaN");return r.s?(G=!1,t=Me(r,e,0,1).times(e),G=!0,r.minus(t)):V(new n(r),i)};S.naturalExponential=S.exp=function(){return Fo(this)};S.naturalLogarithm=S.ln=function(){return Bt(this)};S.negated=S.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};S.plus=S.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Uo(t,e):Vo(t,(e.s=-e.s,e))};S.precision=S.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Ke+e);if(t=Y(i)+1,n=i.d.length-1,r=n*j+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};S.squareRoot=S.sqrt=function(){var e,t,r,n,i,o,s,a=this,f=a.constructor;if(a.s<1){if(!a.s)return new f(0);throw Error(he+"NaN")}for(e=Y(a),G=!1,i=Math.sqrt(+a),i==0||i==1/0?(t=Ce(a.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=pt((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new f(t)):n=new f(i.toString()),r=f.precision,i=s=r+3;;)if(o=n,n=o.plus(Me(a,o,s+2)).times(.5),Ce(o.d).slice(0,s)===(t=Ce(n.d)).slice(0,s)){if(t=t.slice(s-3,s+1),i==s&&t=="4999"){if(V(o,r+1,0),o.times(o).eq(a)){n=o;break}}else if(t!="9999")break;s+=4}return G=!0,V(n,r)};S.times=S.mul=function(e){var t,r,n,i,o,s,a,f,w,v=this,A=v.constructor,R=v.d,C=(e=new A(e)).d;if(!v.s||!e.s)return new A(0);for(e.s*=v.s,r=v.e+e.e,f=R.length,w=C.length,f<w&&(o=R,R=C,C=o,s=f,f=w,w=s),o=[],s=f+w,n=s;n--;)o.push(0);for(n=w;--n>=0;){for(t=0,i=f+n;i>n;)a=o[i]+C[n]*R[i-n-1]+t,o[i--]=a%ee|0,t=a/ee|0;o[i]=(o[i]+t)%ee|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=r,G?V(e,A.precision):e};S.toDecimalPlaces=S.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(Re(e,0,ut),t===void 0?t=n.rounding:Re(t,0,8),V(r,e+Y(r)+1,t))};S.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=ze(n,!0):(Re(e,0,ut),t===void 0?t=i.rounding:Re(t,0,8),n=V(new i(n),e+1,t),r=ze(n,!0,e+1)),r};S.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?ze(i):(Re(e,0,ut),t===void 0?t=o.rounding:Re(t,0,8),n=V(new o(i),e+Y(i)+1,t),r=ze(n.abs(),!1,e+Y(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};S.toInteger=S.toint=function(){var e=this,t=e.constructor;return V(new t(e),Y(e)+1,t.rounding)};S.toNumber=function(){return+this};S.toPower=S.pow=function(e){var t,r,n,i,o,s,a=this,f=a.constructor,w=12,v=+(e=new f(e));if(!e.s)return new f(fe);if(a=new f(a),!a.s){if(e.s<1)throw Error(he+"Infinity");return a}if(a.eq(fe))return a;if(n=f.precision,e.eq(fe))return V(a,n);if(t=e.e,r=e.d.length-1,s=t>=r,o=a.s,s){if((r=v<0?-v:v)<=No){for(i=new f(fe),t=Math.ceil(n/j+4),G=!1;r%2&&(i=i.times(a),Lo(i.d,t)),r=pt(r/2),r!==0;)a=a.times(a),Lo(a.d,t);return G=!0,e.s<0?new f(fe).div(i):V(i,n)}}else if(o<0)throw Error(he+"NaN");return o=o<0&&e.d[Math.max(t,r)]&1?-1:1,a.s=1,G=!1,i=e.times(Bt(a,n+w)),G=!0,i=Fo(i),i.s=o,i};S.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?(r=Y(i),n=ze(i,r<=o.toExpNeg||r>=o.toExpPos)):(Re(e,1,ut),t===void 0?t=o.rounding:Re(t,0,8),i=V(new o(i),e,t),r=Y(i),n=ze(i,e<=r||r<=o.toExpNeg,e)),n};S.toSignificantDigits=S.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(Re(e,1,ut),t===void 0?t=n.rounding:Re(t,0,8)),V(new n(r),e,t)};S.toString=S.valueOf=S.val=S.toJSON=S[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=Y(e),r=e.constructor;return ze(e,t<=r.toExpNeg||t>=r.toExpPos)};Me=function(){function e(n,i){var o,s=0,a=n.length;for(n=n.slice();a--;)o=n[a]*i+s,n[a]=o%ee|0,s=o/ee|0;return s&&n.unshift(s),n}function t(n,i,o,s){var a,f;if(o!=s)f=o>s?1:-1;else for(a=f=0;a<o;a++)if(n[a]!=i[a]){f=n[a]>i[a]?1:-1;break}return f}function r(n,i,o){for(var s=0;o--;)n[o]-=s,s=n[o]<i[o]?1:0,n[o]=s*ee+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s){var a,f,w,v,A,R,C,D,I,M,be,le,$,ce,We,ti,Ee,jr,Qr=n.constructor,zc=n.s==i.s?1:-1,ve=n.d,K=i.d;if(!n.s)return new Qr(n);if(!i.s)throw Error(he+"Division by zero");for(f=n.e-i.e,Ee=K.length,We=ve.length,C=new Qr(zc),D=C.d=[],w=0;K[w]==(ve[w]||0);)++w;if(K[w]>(ve[w]||0)&&--f,o==null?le=o=Qr.precision:s?le=o+(Y(n)-Y(i))+1:le=o,le<0)return new Qr(0);if(le=le/j+2|0,w=0,Ee==1)for(v=0,K=K[0],le++;(w<We||v)&&le--;w++)$=v*ee+(ve[w]||0),D[w]=$/K|0,v=$%K|0;else{for(v=ee/(K[0]+1)|0,v>1&&(K=e(K,v),ve=e(ve,v),Ee=K.length,We=ve.length),ce=Ee,I=ve.slice(0,Ee),M=I.length;M<Ee;)I[M++]=0;jr=K.slice(),jr.unshift(0),ti=K[0],K[1]>=ee/2&&++ti;do v=0,a=t(K,I,Ee,M),a<0?(be=I[0],Ee!=M&&(be=be*ee+(I[1]||0)),v=be/ti|0,v>1?(v>=ee&&(v=ee-1),A=e(K,v),R=A.length,M=I.length,a=t(A,I,R,M),a==1&&(v--,r(A,Ee<R?jr:K,R))):(v==0&&(a=v=1),A=K.slice()),R=A.length,R<M&&A.unshift(0),r(I,A,M),a==-1&&(M=I.length,a=t(K,I,Ee,M),a<1&&(v++,r(I,Ee<M?jr:K,M))),M=I.length):a===0&&(v++,I=[0]),D[w++]=v,a&&I[0]?I[M++]=ve[ce]||0:(I=[ve[ce]],M=1);while((ce++<We||I[0]!==void 0)&&le--)}return D[0]||D.shift(),C.e=f,V(C,s?o+Y(C)+1:o)}}();si=$o(pu);fe=new si(1);Jr=si});var T,ie,l=de(()=>{"use strict";qo();T=class extends Jr{static isDecimal(t){return t instanceof Jr}static random(t=20){{let n=globalThis.crypto.getRandomValues(new Uint8Array(t)).reduce((i,o)=>i+o,"");return new Jr(`0.${n.slice(0,t)}`)}}},ie=T});function bu(){return!1}function ss(){return{dev:0,ino:0,mode:0,nlink:0,uid:0,gid:0,rdev:0,size:0,blksize:0,blocks:0,atimeMs:0,mtimeMs:0,ctimeMs:0,birthtimeMs:0,atime:new Date,mtime:new Date,ctime:new Date,birthtime:new Date}}function Eu(){return ss()}function xu(){return[]}function Pu(e){e(null,[])}function Tu(){return""}function vu(){return""}function Au(){}function Cu(){}function Ru(){}function Su(){}function Iu(){}function ku(){}var Ou,Du,as,ls=de(()=>{"use strict";c();u();p();m();d();l();Ou={},Du={existsSync:bu,lstatSync:ss,statSync:Eu,readdirSync:xu,readdir:Pu,readlinkSync:Tu,realpathSync:vu,chmodSync:Au,renameSync:Cu,mkdirSync:Ru,rmdirSync:Su,rmSync:Iu,unlinkSync:ku,promises:Ou},as=Du});var cs=ne(()=>{"use strict";c();u();p();m();d();l()});function _u(...e){return e.join("/")}function Mu(...e){return e.join("/")}function Lu(e){let t=us(e),r=ps(e),[n,i]=t.split(".");return{root:"/",dir:r,base:t,ext:i,name:n}}function us(e){let t=e.split("/");return t[t.length-1]}function ps(e){return e.split("/").slice(0,-1).join("/")}var ms,Nu,Uu,Zr,ds=de(()=>{"use strict";c();u();p();m();d();l();ms="/",Nu={sep:ms},Uu={basename:us,dirname:ps,join:Mu,parse:Lu,posix:Nu,resolve:_u,sep:ms},Zr=Uu});var fs=ne((Oy,Fu)=>{Fu.exports={name:"@prisma/internals",version:"6.12.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.5","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc","@prisma/schema-engine-wasm":"6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var ui={};qt(ui,{Hash:()=>Ht,createHash:()=>gs,default:()=>ft,randomFillSync:()=>en,randomUUID:()=>Xr,webcrypto:()=>Gt});function Xr(){return globalThis.crypto.randomUUID()}function en(e,t,r){return t!==void 0&&(r!==void 0?e=e.subarray(t,t+r):e=e.subarray(t)),globalThis.crypto.getRandomValues(e)}function gs(e){return new Ht(e)}var Gt,Ht,ft,Ye=de(()=>{"use strict";c();u();p();m();d();l();Gt=globalThis.crypto;Ht=class{#e=[];#t;constructor(t){this.#t=t}update(t){this.#e.push(t)}async digest(){let t=new Uint8Array(this.#e.reduce((i,o)=>i+o.length,0)),r=0;for(let i of this.#e)t.set(i,r),r+=i.length;let n=await globalThis.crypto.subtle.digest(this.#t,t);return new Uint8Array(n)}},ft={webcrypto:Gt,randomUUID:Xr,randomFillSync:en,createHash:gs,Hash:Ht}});var pi=ne((Zy,Bu)=>{Bu.exports={name:"@prisma/engines-version",version:"6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"8047c96bbd92db98a2abc7c9323ce77c02c89dbc"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var ys=ne(tn=>{"use strict";c();u();p();m();d();l();Object.defineProperty(tn,"__esModule",{value:!0});tn.enginesVersion=void 0;tn.enginesVersion=pi().prisma.enginesVersion});var bs=ne((dh,ws)=>{"use strict";c();u();p();m();d();l();ws.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var Ps=ne((Rh,xs)=>{"use strict";c();u();p();m();d();l();xs.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var fi=ne((Mh,Ts)=>{"use strict";c();u();p();m();d();l();var Wu=Ps();Ts.exports=e=>typeof e=="string"?e.replace(Wu(),""):e});var vs=ne((Jh,on)=>{"use strict";c();u();p();m();d();l();on.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};on.exports.default=on.exports});var wi=ne((Kx,Ss)=>{"use strict";c();u();p();m();d();l();Ss.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,f,w,v,A,R,C,D,I,M,be,le,$,ce=[];for(f=0;f<i;f++)ce.push(f+1),ce.push(t.charCodeAt(s+f));for(var We=ce.length-1;a<o-3;)for(M=r.charCodeAt(s+(w=a)),be=r.charCodeAt(s+(v=a+1)),le=r.charCodeAt(s+(A=a+2)),$=r.charCodeAt(s+(R=a+3)),C=a+=4,f=0;f<We;f+=2)D=ce[f],I=ce[f+1],w=e(D,w,v,M,I),v=e(w,v,A,be,I),A=e(v,A,R,le,I),C=e(A,R,C,$,I),ce[f]=C,R=A,A=v,v=w,w=D;for(;a<o;)for(M=r.charCodeAt(s+(w=a)),C=++a,f=0;f<We;f+=2)D=ce[f],ce[f]=C=e(D,w,C,M,ce[f+1]),w=D;return C}}()});var _s=de(()=>{"use strict";c();u();p();m();d();l()});var Ms=de(()=>{"use strict";c();u();p();m();d();l()});var Tn,ta=de(()=>{"use strict";c();u();p();m();d();l();Tn=class{events={};on(t,r){return this.events[t]||(this.events[t]=[]),this.events[t].push(r),this}emit(t,...r){return this.events[t]?(this.events[t].forEach(n=>{n(...r)}),!0):!1}}});var Qi=ne(tt=>{"use strict";c();u();p();m();d();l();Object.defineProperty(tt,"__esModule",{value:!0});tt.anumber=ji;tt.abytes=Ya;tt.ahash=Mm;tt.aexists=Lm;tt.aoutput=Nm;function ji(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function _m(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Ya(e,...t){if(!_m(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function Mm(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");ji(e.outputLen),ji(e.blockLen)}function Lm(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function Nm(e,t){Ya(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var bl=ne(O=>{"use strict";c();u();p();m();d();l();Object.defineProperty(O,"__esModule",{value:!0});O.add5L=O.add5H=O.add4H=O.add4L=O.add3H=O.add3L=O.rotlBL=O.rotlBH=O.rotlSL=O.rotlSH=O.rotr32L=O.rotr32H=O.rotrBL=O.rotrBH=O.rotrSL=O.rotrSH=O.shrSL=O.shrSH=O.toBig=void 0;O.fromBig=Gi;O.split=Za;O.add=ml;var On=BigInt(2**32-1),Hi=BigInt(32);function Gi(e,t=!1){return t?{h:Number(e&On),l:Number(e>>Hi&On)}:{h:Number(e>>Hi&On)|0,l:Number(e&On)|0}}function Za(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=Gi(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var Xa=(e,t)=>BigInt(e>>>0)<<Hi|BigInt(t>>>0);O.toBig=Xa;var el=(e,t,r)=>e>>>r;O.shrSH=el;var tl=(e,t,r)=>e<<32-r|t>>>r;O.shrSL=tl;var rl=(e,t,r)=>e>>>r|t<<32-r;O.rotrSH=rl;var nl=(e,t,r)=>e<<32-r|t>>>r;O.rotrSL=nl;var il=(e,t,r)=>e<<64-r|t>>>r-32;O.rotrBH=il;var ol=(e,t,r)=>e>>>r-32|t<<64-r;O.rotrBL=ol;var sl=(e,t)=>t;O.rotr32H=sl;var al=(e,t)=>e;O.rotr32L=al;var ll=(e,t,r)=>e<<r|t>>>32-r;O.rotlSH=ll;var cl=(e,t,r)=>t<<r|e>>>32-r;O.rotlSL=cl;var ul=(e,t,r)=>t<<r-32|e>>>64-r;O.rotlBH=ul;var pl=(e,t,r)=>e<<r-32|t>>>64-r;O.rotlBL=pl;function ml(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var dl=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);O.add3L=dl;var fl=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;O.add3H=fl;var gl=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);O.add4L=gl;var yl=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;O.add4H=yl;var hl=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);O.add5L=hl;var wl=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;O.add5H=wl;var Um={fromBig:Gi,split:Za,toBig:Xa,shrSH:el,shrSL:tl,rotrSH:rl,rotrSL:nl,rotrBH:il,rotrBL:ol,rotr32H:sl,rotr32L:al,rotlSH:ll,rotlSL:cl,rotlBH:ul,rotlBL:pl,add:ml,add3L:dl,add3H:fl,add4L:gl,add4H:yl,add5H:wl,add5L:hl};O.default=Um});var El=ne(Dn=>{"use strict";c();u();p();m();d();l();Object.defineProperty(Dn,"__esModule",{value:!0});Dn.crypto=void 0;var je=(Ye(),ru(ui));Dn.crypto=je&&typeof je=="object"&&"webcrypto"in je?je.webcrypto:je&&typeof je=="object"&&"randomBytes"in je?je:void 0});var Tl=ne(N=>{"use strict";c();u();p();m();d();l();Object.defineProperty(N,"__esModule",{value:!0});N.Hash=N.nextTick=N.byteSwapIfBE=N.isLE=void 0;N.isBytes=Fm;N.u8=Vm;N.u32=$m;N.createView=qm;N.rotr=Bm;N.rotl=jm;N.byteSwap=Ki;N.byteSwap32=Qm;N.bytesToHex=Gm;N.hexToBytes=Wm;N.asyncLoop=Km;N.utf8ToBytes=Pl;N.toBytes=_n;N.concatBytes=zm;N.checkOpts=Ym;N.wrapConstructor=Zm;N.wrapConstructorWithOpts=Xm;N.wrapXOFConstructorWithOpts=ed;N.randomBytes=td;var St=El(),Ji=Qi();function Fm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Vm(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function $m(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function qm(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Bm(e,t){return e<<32-t|e>>>t}function jm(e,t){return e<<t|e>>>32-t>>>0}N.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function Ki(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}N.byteSwapIfBE=N.isLE?e=>e:e=>Ki(e);function Qm(e){for(let t=0;t<e.length;t++)e[t]=Ki(e[t])}var Hm=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function Gm(e){(0,Ji.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=Hm[e[r]];return t}var Ne={_0:48,_9:57,A:65,F:70,a:97,f:102};function xl(e){if(e>=Ne._0&&e<=Ne._9)return e-Ne._0;if(e>=Ne.A&&e<=Ne.F)return e-(Ne.A-10);if(e>=Ne.a&&e<=Ne.f)return e-(Ne.a-10)}function Wm(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=xl(e.charCodeAt(o)),a=xl(e.charCodeAt(o+1));if(s===void 0||a===void 0){let f=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+f+'" at index '+o)}n[i]=s*16+a}return n}var Jm=async()=>{};N.nextTick=Jm;async function Km(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,N.nextTick)(),n+=o)}}function Pl(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function _n(e){return typeof e=="string"&&(e=Pl(e)),(0,Ji.abytes)(e),e}function zm(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,Ji.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var Wi=class{clone(){return this._cloneInto()}};N.Hash=Wi;function Ym(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function Zm(e){let t=n=>e().update(_n(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function Xm(e){let t=(n,i)=>e(i).update(_n(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function ed(e){let t=(n,i)=>e(i).update(_n(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function td(e=32){if(St.crypto&&typeof St.crypto.getRandomValues=="function")return St.crypto.getRandomValues(new Uint8Array(e));if(St.crypto&&typeof St.crypto.randomBytes=="function")return St.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var Ol=ne(H=>{"use strict";c();u();p();m();d();l();Object.defineProperty(H,"__esModule",{value:!0});H.shake256=H.shake128=H.keccak_512=H.keccak_384=H.keccak_256=H.keccak_224=H.sha3_512=H.sha3_384=H.sha3_256=H.sha3_224=H.Keccak=void 0;H.keccakP=Il;var It=Qi(),hr=bl(),Ue=Tl(),Cl=[],Rl=[],Sl=[],rd=BigInt(0),yr=BigInt(1),nd=BigInt(2),id=BigInt(7),od=BigInt(256),sd=BigInt(113);for(let e=0,t=yr,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],Cl.push(2*(5*n+r)),Rl.push((e+1)*(e+2)/2%64);let i=rd;for(let o=0;o<7;o++)t=(t<<yr^(t>>id)*sd)%od,t&nd&&(i^=yr<<(yr<<BigInt(o))-yr);Sl.push(i)}var[ad,ld]=(0,hr.split)(Sl,!0),vl=(e,t,r)=>r>32?(0,hr.rotlBH)(e,t,r):(0,hr.rotlSH)(e,t,r),Al=(e,t,r)=>r>32?(0,hr.rotlBL)(e,t,r):(0,hr.rotlSL)(e,t,r);function Il(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let a=(s+8)%10,f=(s+2)%10,w=r[f],v=r[f+1],A=vl(w,v,1)^r[a],R=Al(w,v,1)^r[a+1];for(let C=0;C<50;C+=10)e[s+C]^=A,e[s+C+1]^=R}let i=e[2],o=e[3];for(let s=0;s<24;s++){let a=Rl[s],f=vl(i,o,a),w=Al(i,o,a),v=Cl[s];i=e[v],o=e[v+1],e[v]=f,e[v+1]=w}for(let s=0;s<50;s+=10){for(let a=0;a<10;a++)r[a]=e[s+a];for(let a=0;a<10;a++)e[s+a]^=~r[(a+2)%10]&r[(a+4)%10]}e[0]^=ad[n],e[1]^=ld[n]}r.fill(0)}var wr=class e extends Ue.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,It.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,Ue.u32)(this.state)}keccak(){Ue.isLE||(0,Ue.byteSwap32)(this.state32),Il(this.state32,this.rounds),Ue.isLE||(0,Ue.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,It.aexists)(this);let{blockLen:r,state:n}=this;t=(0,Ue.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let a=0;a<s;a++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,It.aexists)(this,!1),(0,It.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,It.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,It.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};H.Keccak=wr;var Qe=(e,t,r)=>(0,Ue.wrapConstructor)(()=>new wr(t,e,r));H.sha3_224=Qe(6,144,224/8);H.sha3_256=Qe(6,136,256/8);H.sha3_384=Qe(6,104,384/8);H.sha3_512=Qe(6,72,512/8);H.keccak_224=Qe(1,144,224/8);H.keccak_256=Qe(1,136,256/8);H.keccak_384=Qe(1,104,384/8);H.keccak_512=Qe(1,72,512/8);var kl=(e,t,r)=>(0,Ue.wrapXOFConstructorWithOpts)((n={})=>new wr(t,e,n.dkLen===void 0?r:n.dkLen,!0));H.shake128=kl(31,168,128/8);H.shake256=kl(31,136,256/8)});var Vl=ne((nN,He)=>{"use strict";c();u();p();m();d();l();var{sha3_512:cd}=Ol(),_l=24,br=32,zi=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function Ml(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var Ll=(e="")=>Ml(cd(e)).toString(36).slice(1),Dl=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),ud=e=>Dl[Math.floor(e()*Dl.length)],Nl=({globalObj:e=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+zi(br,t):zi(br,t);return Ll(n).substring(0,br)},Ul=e=>()=>e++,pd=476782367,Fl=({random:e=Math.random,counter:t=Ul(Math.floor(e()*pd)),length:r=_l,fingerprint:n=Nl({random:e})}={})=>function(){let o=ud(e),s=Date.now().toString(36),a=t().toString(36),f=zi(r,e),w=`${s+f+a+n}`;return`${o+Ll(w).substring(1,r)}`},md=Fl(),dd=(e,{minLength:t=2,maxLength:r=br}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};He.exports.getConstants=()=>({defaultLength:_l,bigLength:br});He.exports.init=Fl;He.exports.createId=md;He.exports.bufToBigInt=Ml;He.exports.createCounter=Ul;He.exports.createFingerprint=Nl;He.exports.isCuid=dd});var $l=ne((uN,Er)=>{"use strict";c();u();p();m();d();l();var{createId:fd,init:gd,getConstants:yd,isCuid:hd}=Vl();Er.exports.createId=fd;Er.exports.init=gd;Er.exports.getConstants=yd;Er.exports.isCuid=hd});c();u();p();m();d();l();var Qo={};qt(Qo,{defineExtension:()=>Bo,getExtensionContext:()=>jo});c();u();p();m();d();l();c();u();p();m();d();l();function Bo(e){return typeof e=="function"?e:t=>t.$extends(e)}c();u();p();m();d();l();function jo(e){return e}var Go={};qt(Go,{validator:()=>Ho});c();u();p();m();d();l();c();u();p();m();d();l();function Ho(...e){return t=>t}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var ai,Wo,Jo,Ko,zo=!0;typeof g<"u"&&({FORCE_COLOR:ai,NODE_DISABLE_COLORS:Wo,NO_COLOR:Jo,TERM:Ko}=g.env||{},zo=g.stdout&&g.stdout.isTTY);var fu={enabled:!Wo&&Jo==null&&Ko!=="dumb"&&(ai!=null&&ai!=="0"||zo)};function q(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!fu.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var xg=q(0,0),Kr=q(1,22),zr=q(2,22),Pg=q(3,23),Yr=q(4,24),Tg=q(7,27),vg=q(8,28),Ag=q(9,29),Cg=q(30,39),mt=q(31,39),Yo=q(32,39),Zo=q(33,39),Xo=q(34,39),Rg=q(35,39),es=q(36,39),Sg=q(37,39),ts=q(90,39),Ig=q(90,39),kg=q(40,49),Og=q(41,49),Dg=q(42,49),_g=q(43,49),Mg=q(44,49),Lg=q(45,49),Ng=q(46,49),Ug=q(47,49);c();u();p();m();d();l();var gu=100,rs=["green","yellow","blue","magenta","cyan","red"],jt=[],ns=Date.now(),yu=0,li=typeof g<"u"?g.env:{};globalThis.DEBUG??=li.DEBUG??"";globalThis.DEBUG_COLORS??=li.DEBUG_COLORS?li.DEBUG_COLORS==="true":!0;var Qt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function hu(e){let t={color:rs[yu++%rs.length],enabled:Qt.enabled(e),namespace:e,log:Qt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&jt.push([o,...n]),jt.length>gu&&jt.shift(),Qt.enabled(o)||i){let f=n.map(v=>typeof v=="string"?v:wu(v)),w=`+${Date.now()-ns}ms`;ns=Date.now(),a(o,...f,w)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var J=new Proxy(hu,{get:(e,t)=>Qt[t],set:(e,t,r)=>Qt[t]=r});function wu(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function is(e=7500){let t=jt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function os(){jt.length=0}c();u();p();m();d();l();c();u();p();m();d();l();var Vu=fs(),ci=Vu.version;c();u();p();m();d();l();function dt(e){let t=$u();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":qu(e))}function $u(){let e=g.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function qu(e){return e?.previewFeatures.includes("queryCompiler")?"client":"library"}c();u();p();m();d();l();var hs="prisma+postgres",rn=`${hs}:`;function nn(e){return e?.toString().startsWith(`${rn}//`)??!1}function mi(e){if(!nn(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")||t.includes("[::1]")}var Jt={};qt(Jt,{error:()=>Hu,info:()=>Qu,log:()=>ju,query:()=>Gu,should:()=>Es,tags:()=>Wt,warn:()=>di});c();u();p();m();d();l();var Wt={error:mt("prisma:error"),warn:Zo("prisma:warn"),info:es("prisma:info"),query:Xo("prisma:query")},Es={warn:()=>!g.env.PRISMA_DISABLE_WARNINGS};function ju(...e){console.log(...e)}function di(e,...t){Es.warn()&&console.warn(`${Wt.warn} ${e}`,...t)}function Qu(e,...t){console.info(`${Wt.info} ${e}`,...t)}function Hu(e,...t){console.error(`${Wt.error} ${e}`,...t)}function Gu(e,...t){console.log(`${Wt.query} ${e}`,...t)}c();u();p();m();d();l();function xe(e,t){throw new Error(t)}c();u();p();m();d();l();function gi(e,t){return Object.prototype.hasOwnProperty.call(e,t)}c();u();p();m();d();l();function gt(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}c();u();p();m();d();l();function yi(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}c();u();p();m();d();l();function k(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}c();u();p();m();d();l();var As=new Set,sn=(e,t,...r)=>{As.has(e)||(As.add(e),di(t,...r))};var U=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};k(U,"PrismaClientInitializationError");c();u();p();m();d();l();var Z=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};k(Z,"PrismaClientKnownRequestError");c();u();p();m();d();l();var pe=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};k(pe,"PrismaClientRustPanicError");c();u();p();m();d();l();var se=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};k(se,"PrismaClientUnknownRequestError");c();u();p();m();d();l();var oe=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};k(oe,"PrismaClientValidationError");c();u();p();m();d();l();l();function Ze(e){return e===null?e:Array.isArray(e)?e.map(Ze):typeof e=="object"?Ju(e)?Ku(e):e.constructor!==null&&e.constructor.name!=="Object"?e:gt(e,Ze):e}function Ju(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function Ku({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new ie(t);case"Json":return JSON.parse(t);default:xe(t,"Unknown tagged value")}}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var Se=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};c();u();p();m();d();l();function $e(e){return e.substring(0,1).toLowerCase()+e.substring(1)}c();u();p();m();d();l();function Rs(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}c();u();p();m();d();l();function Kt(e){let t;return{get(){return t||(t={value:e()}),t.value}}}c();u();p();m();d();l();function zu(e){return{models:hi(e.models),enums:hi(e.enums),types:hi(e.types)}}function hi(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}c();u();p();m();d();l();function yt(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function an(e){return e.toString()!=="Invalid Date"}c();u();p();m();d();l();l();function ht(e){return T.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}c();u();p();m();d();l();c();u();p();m();d();l();var ln={};qt(ln,{ModelAction:()=>zt,datamodelEnumToSchemaEnum:()=>Yu});c();u();p();m();d();l();c();u();p();m();d();l();function Yu(e){return{name:e.name,values:e.values.map(t=>t.name)}}c();u();p();m();d();l();var zt=($=>($.findUnique="findUnique",$.findUniqueOrThrow="findUniqueOrThrow",$.findFirst="findFirst",$.findFirstOrThrow="findFirstOrThrow",$.findMany="findMany",$.create="create",$.createMany="createMany",$.createManyAndReturn="createManyAndReturn",$.update="update",$.updateMany="updateMany",$.updateManyAndReturn="updateManyAndReturn",$.upsert="upsert",$.delete="delete",$.deleteMany="deleteMany",$.groupBy="groupBy",$.count="count",$.aggregate="aggregate",$.findRaw="findRaw",$.aggregateRaw="aggregateRaw",$))(zt||{});var Zu=Ae(bs());var Xu={red:mt,gray:ts,dim:zr,bold:Kr,underline:Yr,highlightSource:e=>e.highlight()},ep={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function tp({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function rp({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],f=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${f}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${f}`)),t&&a.push(s.underline(np(t))),i){a.push("");let w=[i.toString()];o&&(w.push(o),w.push(s.dim(")"))),a.push(w.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function np(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function cn(e){let t=e.showColors?Xu:ep,r;return typeof $getTemplateParameters<"u"?r=$getTemplateParameters(e,t):r=tp(e),rp(r,t)}c();u();p();m();d();l();var Ns=Ae(wi());c();u();p();m();d();l();function Os(e,t,r){let n=Ds(e),i=ip(n),o=sp(i);o?un(o,t,r):t.addErrorMessage(()=>"Unknown error")}function Ds(e){return e.errors.flatMap(t=>t.kind==="Union"?Ds(t):[t])}function ip(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:op(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function op(e,t){return[...new Set(e.concat(t))]}function sp(e){return yi(e,(t,r)=>{let n=Is(t),i=Is(r);return n!==i?n-i:ks(t)-ks(r)})}function Is(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function ks(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}c();u();p();m();d();l();var ge=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};c();u();p();m();d();l();c();u();p();m();d();l();Ms();c();u();p();m();d();l();var wt=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};_s();c();u();p();m();d();l();c();u();p();m();d();l();var pn=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};c();u();p();m();d();l();var mn=e=>e,dn={bold:mn,red:mn,green:mn,dim:mn,enabled:!1},Ls={bold:Kr,red:mt,green:Yo,dim:zr,enabled:!0},bt={write(e){e.writeLine(",")}};c();u();p();m();d();l();var Ie=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};c();u();p();m();d();l();var qe=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var Et=class extends qe{items=[];addItem(t){return this.items.push(new pn(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new Ie("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(bt,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var xt=class e extends qe{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof e?a=o.value.getField(s):o.value instanceof Et&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new Ie("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(bt,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};c();u();p();m();d();l();var te=class extends qe{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new Ie(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};c();u();p();m();d();l();var Yt=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(bt,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function un(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":ap(e,t);break;case"IncludeOnScalar":lp(e,t);break;case"EmptySelection":cp(e,t,r);break;case"UnknownSelectionField":dp(e,t);break;case"InvalidSelectionValue":fp(e,t);break;case"UnknownArgument":gp(e,t);break;case"UnknownInputField":yp(e,t);break;case"RequiredArgumentMissing":hp(e,t);break;case"InvalidArgumentType":wp(e,t);break;case"InvalidArgumentValue":bp(e,t);break;case"ValueTooLarge":Ep(e,t);break;case"SomeFieldsMissing":xp(e,t);break;case"TooManyFieldsGiven":Pp(e,t);break;case"Union":Os(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function ap(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function lp(e,t){let[r,n]=Zt(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new ge(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${Xt(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function cp(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){up(e,t,i);return}if(n.hasField("select")){pp(e,t);return}}if(r?.[$e(e.outputType.name)]){mp(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function up(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new ge(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function pp(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Vs(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${Xt(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function mp(e,t){let r=new Yt;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new ge("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=Zt(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let f=a?.value.asObject()??new xt;f.addSuggestion(n),a.value=f}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function dp(e,t){let r=$s(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":Vs(n,e.outputType);break;case"include":Tp(n,e.outputType);break;case"omit":vp(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(Xt(n)),i.join(" ")})}function fp(e,t){let r=$s(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function gp(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),Ap(n,e.arguments)),t.addErrorMessage(i=>Us(i,r,e.arguments.map(o=>o.name)))}function yp(e,t){let[r,n]=Zt(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&qs(o,e.inputType)}t.addErrorMessage(o=>Us(o,n,e.inputType.fields.map(s=>s.name)))}function Us(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=Rp(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(Xt(e)),n.join(" ")}function hp(e,t){let r;t.addErrorMessage(f=>r?.value instanceof te&&r.value.text==="null"?`Argument \`${f.green(o)}\` must not be ${f.red("null")}.`:`Argument \`${f.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=Zt(e.argumentPath),s=new Yt,a=n.getDeepFieldValue(i)?.asObject();if(a)if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let f of e.inputTypes[0].fields)s.addField(f.name,f.typeNames.join(" | "));a.addSuggestion(new ge(o,s).makeRequired())}else{let f=e.inputTypes.map(Fs).join(" | ");a.addSuggestion(new ge(o,f).makeRequired())}}function Fs(e){return e.kind==="list"?`${Fs(e.elementType)}[]`:e.name}function wp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=fn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function bp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=fn("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function Ep(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof te&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function xp(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&qs(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${fn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(Xt(i)),o.join(" ")})}function Pp(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${fn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Vs(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ge(r.name,"true"))}function Tp(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new ge(r.name,"true"))}function vp(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new ge(r.name,"true"))}function Ap(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new ge(r.name,r.typeNames.join(" | ")))}function $s(e,t){let[r,n]=Zt(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),f=o?.getField(n);return o&&f?{parentKind:"select",parent:o,field:f,fieldName:n}:(f=s?.getField(n),s&&f?{parentKind:"include",field:f,parent:s,fieldName:n}:(f=a?.getField(n),a&&f?{parentKind:"omit",field:f,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function qs(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ge(r.name,r.typeNames.join(" | ")))}function Zt(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function Xt({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function fn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var Cp=3;function Rp(e,t){let r=1/0,n;for(let i of t){let o=(0,Ns.default)(e,i);o>Cp||o<r&&(r=o,n=i)}return n}c();u();p();m();d();l();c();u();p();m();d();l();var er=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function Pt(e){return e instanceof er}c();u();p();m();d();l();var gn=Symbol(),Ei=new WeakMap,Le=class{constructor(t){t===gn?Ei.set(this,`Prisma.${this._getName()}`):Ei.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return Ei.get(this)}},tr=class extends Le{_getNamespace(){return"NullTypes"}},rr=class extends tr{#e};Pi(rr,"DbNull");var nr=class extends tr{#e};Pi(nr,"JsonNull");var ir=class extends tr{#e};Pi(ir,"AnyNull");var xi={classes:{DbNull:rr,JsonNull:nr,AnyNull:ir},instances:{DbNull:new rr(gn),JsonNull:new nr(gn),AnyNull:new ir(gn)}};function Pi(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}c();u();p();m();d();l();var Bs=": ",yn=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+Bs.length}write(t){let r=new Ie(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(Bs).write(this.value)}};var Ti=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function Tt(e){return new Ti(js(e))}function js(e){let t=new xt;for(let[r,n]of Object.entries(e)){let i=new yn(r,Qs(n));t.addField(i)}return t}function Qs(e){if(typeof e=="string")return new te(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new te(String(e));if(typeof e=="bigint")return new te(`${e}n`);if(e===null)return new te("null");if(e===void 0)return new te("undefined");if(ht(e))return new te(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return y.isBuffer(e)?new te(`Buffer.alloc(${e.byteLength})`):new te(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=an(e)?e.toISOString():"Invalid Date";return new te(`new Date("${t}")`)}return e instanceof Le?new te(`Prisma.${e._getName()}`):Pt(e)?new te(`prisma.${$e(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?Sp(e):typeof e=="object"?js(e):new te(Object.prototype.toString.call(e))}function Sp(e){let t=new Et;for(let r of e)t.addItem(Qs(r));return t}function hn(e,t){let r=t==="pretty"?Ls:dn,n=e.renderAllMessages(r),i=new wt(0,{colors:r}).write(e).toString();return{message:n,args:i}}function wn({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=Tt(e);for(let A of t)un(A,a,s);let{message:f,args:w}=hn(a,r),v=cn({message:f,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:w});throw new oe(v,{clientVersion:o})}c();u();p();m();d();l();c();u();p();m();d();l();function ke(e){return e.replace(/^./,t=>t.toLowerCase())}c();u();p();m();d();l();function Gs(e,t,r){let n=ke(r);return!t.result||!(t.result.$allModels||t.result[n])?e:Ip({...e,...Hs(t.name,e,t.result.$allModels),...Hs(t.name,e,t.result[n])})}function Ip(e){let t=new Se,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return gt(e,n=>({...n,needs:r(n.name,new Set)}))}function Hs(e,t,r){return r?gt(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:kp(t,o,i)})):{}}function kp(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function Ws(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function Js(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var bn=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new Se;modelExtensionsCache=new Se;queryCallbacksCache=new Se;clientExtensions=Kt(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=Kt(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>Gs(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=ke(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},vt=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new bn(t))}isEmpty(){return this.head===void 0}append(t){return new e(new bn(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};c();u();p();m();d();l();var En=class{constructor(t){this.name=t}};function Ks(e){return e instanceof En}function Op(e){return new En(e)}c();u();p();m();d();l();c();u();p();m();d();l();var zs=Symbol(),or=class{constructor(t){if(t!==zs)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?vi:t}},vi=new or(zs);function Oe(e){return e instanceof or}var Dp={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},Ys="explicitly `undefined` values are not allowed";function Ci({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=vt.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:f,previewFeatures:w,globalOmit:v}){let A=new Ai({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:f,previewFeatures:w,globalOmit:v});return{modelName:e,action:Dp[t],query:sr(r,A)}}function sr({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:Xs(r,n),selection:_p(e,t,i,n)}}function _p(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),Up(e,n)):Mp(n,t,r)}function Mp(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&Lp(n,t,e),Np(n,r,e),n}function Lp(e,t,r){for(let[n,i]of Object.entries(t)){if(Oe(i))continue;let o=r.nestSelection(n);if(Ri(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=sr(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=sr(i,o)}}function Np(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=Js(i,n);for(let[s,a]of Object.entries(o)){if(Oe(a))continue;Ri(a,r.nestSelection(s));let f=r.findField(s);n?.[s]&&!f||(e[s]=!a)}}function Up(e,t){let r={},n=t.getComputedFields(),i=Ws(e,n);for(let[o,s]of Object.entries(i)){if(Oe(s))continue;let a=t.nestSelection(o);Ri(s,a);let f=t.findField(o);if(!(n?.[o]&&!f)){if(s===!1||s===void 0||Oe(s)){r[o]=!1;continue}if(s===!0){f?.kind==="object"?r[o]=sr({},a):r[o]=!0;continue}r[o]=sr(s,a)}}return r}function Zs(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(yt(e)){if(an(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(Ks(e))return{$type:"Param",value:e.name};if(Pt(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return Fp(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:y.from(r,n,i).toString("base64")}}if(Vp(e))return e.values;if(ht(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Le){if(e!==xi.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if($p(e))return e.toJSON();if(typeof e=="object")return Xs(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function Xs(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);Oe(i)||(i!==void 0?r[n]=Zs(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:Ys}))}return r}function Fp(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||Oe(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(Zs(o,i))}return r}function Vp(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function $p(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function Ri(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:Ys})}var Ai=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){wn({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[$e(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:xe(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};c();u();p();m();d();l();function ea(e){if(!e._hasPreviewFlag("metrics"))throw new oe("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var ar=class{_client;constructor(t){this._client=t}prometheus(t){return ea(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return ea(this._client),this._client._engine.metrics({format:"json",...t})}};c();u();p();m();d();l();function qp(e,t){let r=Kt(()=>Bp(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function Bp(e){throw new Error("Prisma.dmmf is not available when running in edge runtimes.")}function Si(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}c();u();p();m();d();l();var Ii=new WeakMap,xn="$$PrismaTypedSql",lr=class{constructor(t,r){Ii.set(this,{sql:t,values:r}),Object.defineProperty(this,xn,{value:xn})}get sql(){return Ii.get(this).sql}get values(){return Ii.get(this).values}};function jp(e){return(...t)=>new lr(e,t)}function Pn(e){return e!=null&&e[xn]===xn}c();u();p();m();d();l();var Kc=Ae(pi());c();u();p();m();d();l();ta();ls();ds();c();u();p();m();d();l();var ye=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let f=0;for(;f<s.values.length;)this.values[o++]=s.values[f++],this.strings[o]=s.strings[f];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function Qp(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new ye([r,...Array(e.length-1).fill(t),n],e)}function ra(e){return new ye([e],[])}var Hp=ra("");function na(e,...t){return new ye(e,t)}c();u();p();m();d();l();c();u();p();m();d();l();function cr(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}c();u();p();m();d();l();function ae(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}c();u();p();m();d();l();function Xe(e){let t=new Se;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}c();u();p();m();d();l();c();u();p();m();d();l();var vn={enumerable:!0,configurable:!0,writable:!0};function An(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>vn,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var ia=Symbol.for("nodejs.util.inspect.custom");function Pe(e,t){let r=Gp(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=oa(Reflect.ownKeys(o),r),a=oa(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let f=r.get(s);return f?f.getPropertyDescriptor?{...vn,...f?.getPropertyDescriptor(s)}:vn:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[ia]=function(){let o={...this};return delete o[ia],o},i}function Gp(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function oa(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}c();u();p();m();d();l();function At(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}c();u();p();m();d();l();function Ct(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}c();u();p();m();d();l();function sa(e){if(e===void 0)return"";let t=Tt(e);return new wt(0,{colors:dn}).write(t).toString()}c();u();p();m();d();l();var Wp="P2037";function Cn({error:e,user_facing_error:t},r,n){return t.error_code?new Z(Jp(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new se(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function Jp(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===Wp&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var ki=class{getLocation(){return null}};function Be(e){return typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new ki}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var aa={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function Rt(e={}){let t=zp(e);return Object.entries(t).reduce((n,[i,o])=>(aa[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function zp(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function Rn(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function la(e,t){let r=Rn(e);return t({action:"aggregate",unpacker:r,argsMapper:Rt})(e)}c();u();p();m();d();l();function Yp(e={}){let{select:t,...r}=e;return typeof t=="object"?Rt({...r,_count:t}):Rt({...r,_count:{_all:!0}})}function Zp(e={}){return typeof e.select=="object"?t=>Rn(e)(t)._count:t=>Rn(e)(t)._count._all}function ca(e,t){return t({action:"count",unpacker:Zp(e),argsMapper:Yp})(e)}c();u();p();m();d();l();function Xp(e={}){let t=Rt(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function em(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function ua(e,t){return t({action:"groupBy",unpacker:em(e),argsMapper:Xp})(e)}function pa(e,t,r){if(t==="aggregate")return n=>la(n,r);if(t==="count")return n=>ca(n,r);if(t==="groupBy")return n=>ua(n,r)}c();u();p();m();d();l();function ma(e,t){let r=t.fields.filter(i=>!i.relationName),n=Rs(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new er(e,o,s.type,s.isList,s.kind==="enum")},...An(Object.keys(n))})}c();u();p();m();d();l();c();u();p();m();d();l();var da=e=>Array.isArray(e)?e:e.split("."),Oi=(e,t)=>da(t).reduce((r,n)=>r&&r[n],e),fa=(e,t,r)=>da(t).reduceRight((n,i,o,s)=>Object.assign({},Oi(e,s.slice(0,o)),{[i]:n}),r);function tm(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function rm(e,t,r){return t===void 0?e??{}:fa(t,r,e||!0)}function Di(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((f,w)=>({...f,[w.name]:w}),{});return f=>{let w=Be(e._errorFormat),v=tm(n,i),A=rm(f,o,v),R=r({dataPath:v,callsite:w})(A),C=nm(e,t);return new Proxy(R,{get(D,I){if(!C.includes(I))return D[I];let be=[a[I].type,r,I],le=[v,A];return Di(e,...be,...le)},...An([...C,...Object.getOwnPropertyNames(R)])})}}function nm(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var im=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],om=["aggregate","count","groupBy"];function _i(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[sm(e,t),lm(e,t),cr(r),ae("name",()=>t),ae("$name",()=>t),ae("$parent",()=>e._appliedParent)];return Pe({},n)}function sm(e,t){let r=ke(t),n=Object.keys(zt).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>f=>{let w=Be(e._errorFormat);return e._createPrismaPromise(v=>{let A={args:f,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:v,callsite:w};return e._request({...A,...a})},{action:o,args:f,model:t})};return im.includes(o)?Di(e,t,s):am(i)?pa(e,i,s):s({})}}}function am(e){return om.includes(e)}function lm(e,t){return Xe(ae("fields",()=>{let r=e._runtimeDataModel.models[t];return ma(t,r)}))}c();u();p();m();d();l();function ga(e){return e.replace(/^./,t=>t.toUpperCase())}var Mi=Symbol();function ur(e){let t=[cm(e),um(e),ae(Mi,()=>e),ae("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(cr(r)),Pe(e,t)}function cm(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function um(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(ke),n=[...new Set(t.concat(r))];return Xe({getKeys(){return n},getPropertyValue(i){let o=ga(i);if(e._runtimeDataModel.models[o]!==void 0)return _i(e,o);if(e._runtimeDataModel.models[i]!==void 0)return _i(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function ya(e){return e[Mi]?e[Mi]:e}function ha(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return ur(t)}c();u();p();m();d();l();c();u();p();m();d();l();function wa({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let f of Object.values(o)){if(n){if(n[f.name])continue;let w=f.needs.filter(v=>n[v]);w.length>0&&a.push(At(w))}else if(r){if(!r[f.name])continue;let w=f.needs.filter(v=>!r[v]);w.length>0&&a.push(At(w))}pm(e,f.needs)&&s.push(mm(f,Pe(e,s)))}return s.length>0||a.length>0?Pe(e,[...s,...a]):e}function pm(e,t){return t.every(r=>gi(e,r))}function mm(e,t){return Xe(ae(e.name,()=>e.compute(t)))}c();u();p();m();d();l();function Sn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=Sn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&ba({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&ba({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function ba({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||Oe(s))continue;let f=n.models[r].fields.find(v=>v.name===o);if(!f||f.kind!=="object"||!f.relationName)continue;let w=typeof s=="object"?s:{};t[o]=Sn({visitor:i,result:t[o],args:w,modelName:f.type,runtimeDataModel:n})}}function Ea({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:Sn({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,f,w)=>{let v=ke(f);return wa({result:a,modelName:v,select:w.select,omit:w.select?void 0:{...o?.[v],...w.omit},extensions:n})}})}c();u();p();m();d();l();c();u();p();m();d();l();l();c();u();p();m();d();l();var dm=["$connect","$disconnect","$on","$transaction","$use","$extends"],xa=dm;function Pa(e){if(e instanceof ye)return fm(e);if(Pn(e))return gm(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=pr(e[n]);return r}let t={};for(let r in e)t[r]=pr(e[r]);return t}function fm(e){return new ye(e.strings,e.values)}function gm(e){return new lr(e.sql,e.values)}function pr(e){if(typeof e!="object"||e==null||e instanceof Le||Pt(e))return e;if(ht(e))return new ie(e.toFixed());if(yt(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=pr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:pr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=pr(e[r]);return t}xe(e,"Unknown value")}function va(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Pa(t.args??{}),__internalParams:t,query:(s,a=t)=>{let f=a.customDataProxyFetch;return a.customDataProxyFetch=Sa(o,f),a.args=s,va(e,a,r,n+1)}})})}function Aa(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return va(e,t,s)}function Ca(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?Ra(r,n,0,e):e(r)}}function Ra(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let f=a.customDataProxyFetch;return a.customDataProxyFetch=Sa(i,f),Ra(a,t,r+1,n)}})}var Ta=e=>e;function Sa(e=Ta,t=Ta){return r=>e(t(r))}c();u();p();m();d();l();var Ia=J("prisma:client"),ka={Vercel:"vercel","Netlify CI":"netlify"};function Oa({postinstall:e,ciName:t,clientVersion:r}){if(Ia("checkPlatformCaching:postinstall",e),Ia("checkPlatformCaching:ciName",t),e===!0&&t&&t in ka){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${ka[t]}-build`;throw console.error(n),new U(n,r)}}c();u();p();m();d();l();function Da(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}c();u();p();m();d();l();c();u();p();m();d();l();var ym=()=>globalThis.process?.release?.name==="node",hm=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,wm=()=>!!globalThis.Deno,bm=()=>typeof globalThis.Netlify=="object",Em=()=>typeof globalThis.EdgeRuntime=="object",xm=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function Pm(){return[[bm,"netlify"],[Em,"edge-light"],[xm,"workerd"],[wm,"deno"],[hm,"bun"],[ym,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var Tm={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function mr(){let e=Pm();return{id:e,prettyName:Tm[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}c();u();p();m();d();l();c();u();p();m();d();l();var Li=Ae(fi());c();u();p();m();d();l();function _a(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}c();u();p();m();d();l();function Ma(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}c();u();p();m();d();l();var La=Ae(vs());function Na({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,La.default)({user:t,repo:r,template:n,title:e,body:i})}function Ua({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=is(6e3-(s?.length??0)),f=Ma((0,Li.default)(a)),w=n?`# Description
\`\`\`
${n}
\`\`\``:"",v=(0,Li.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${g.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${w}

## Logs
\`\`\`
${f}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?_a(s):""}
\`\`\`
`),A=Na({title:r,body:v});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${Yr(A)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();l();c();u();p();m();d();l();l();function Q(e,t){throw new Error(t)}function Ni(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>Ni(e[r],t[r]))}function dr(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>{if(typeof e[o]==typeof t[o]&&typeof e[o]!="object")return e[o]===t[o];if(ie.isDecimal(e[o])||ie.isDecimal(t[o])){let s=Fa(e[o]),a=Fa(t[o]);return s&&a&&s.equals(a)}else if(e[o]instanceof Uint8Array||t[o]instanceof Uint8Array){let s=Va(e[o]),a=Va(t[o]);return s&&a&&s.equals(a)}else{if(e[o]instanceof Date||t[o]instanceof Date)return $a(e[o])?.getTime()===$a(t[o])?.getTime();if(typeof e[o]=="bigint"||typeof t[o]=="bigint")return qa(e[o])===qa(t[o]);if(typeof e[o]=="number"||typeof t[o]=="number")return Ba(e[o])===Ba(t[o])}return Ni(e[o],t[o])})}function Fa(e){return ie.isDecimal(e)?e:typeof e=="number"||typeof e=="string"?new ie(e):void 0}function Va(e){return y.isBuffer(e)?e:e instanceof Uint8Array?y.from(e.buffer,e.byteOffset,e.byteLength):typeof e=="string"?y.from(e,"base64"):void 0}function $a(e){return e instanceof Date?e:typeof e=="string"||typeof e=="number"?new Date(e):void 0}function qa(e){return typeof e=="bigint"?e:typeof e=="number"||typeof e=="string"?BigInt(e):void 0}function Ba(e){return typeof e=="number"?e:typeof e=="string"?Number(e):void 0}function fr(e){return JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():r instanceof Uint8Array?y.from(r).toString("base64"):r)}var W=class extends Error{name="DataMapperError"};function Ha(e,t,r){switch(t.type){case"AffectedRows":if(typeof e!="number")throw new W(`Expected an affected rows count, got: ${typeof e} (${e})`);return{count:e};case"Object":return Ui(e,t.fields,r);case"Value":return Fi(e,"<result>",t.resultType,r);default:Q(t,`Invalid data mapping type: '${t.type}'`)}}function Ui(e,t,r){if(e===null)return null;if(Array.isArray(e))return e.map(i=>ja(i,t,r));if(typeof e=="object")return ja(e,t,r);if(typeof e=="string"){let n;try{n=JSON.parse(e)}catch(i){throw new W("Expected an array or object, got a string that is not valid JSON",{cause:i})}return Ui(n,t,r)}throw new W(`Expected an array or an object, got: ${typeof e}`)}function ja(e,t,r){if(typeof e!="object")throw new W(`Expected an object, but got '${typeof e}'`);let n={};for(let[i,o]of Object.entries(t))switch(o.type){case"AffectedRows":throw new W(`Unexpected 'AffectedRows' node in data mapping for field '${i}'`);case"Object":{if(o.serializedName!==null&&!Object.hasOwn(e,o.serializedName))throw new W(`Missing data field (Object): '${i}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`);let s=o.serializedName!==null?e[o.serializedName]:e;n[i]=Ui(s,o.fields,r);break}case"Value":{let s=o.dbName;if(Object.hasOwn(e,s))n[i]=Fi(e[s],s,o.resultType,r);else throw new W(`Missing data field (Value): '${s}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`)}break;default:Q(o,`DataMapper: Invalid data mapping node type: '${o.type}'`)}return n}function Fi(e,t,r,n){if(e===null)return r.type==="Array"?[]:null;switch(r.type){case"Any":return e;case"String":{if(typeof e!="string")throw new W(`Expected a string in column '${t}', got ${typeof e}: ${e}`);return e}case"Int":switch(typeof e){case"number":return Math.trunc(e);case"string":{let i=Math.trunc(Number(e));if(Number.isNaN(i)||!Number.isFinite(i))throw new W(`Expected an integer in column '${t}', got string: ${e}`);if(!Number.isSafeInteger(i))throw new W(`Integer value in column '${t}' is too large to represent as a JavaScript number without loss of precision, got: ${e}. Consider using BigInt type.`);return i}default:throw new W(`Expected an integer in column '${t}', got ${typeof e}: ${e}`)}case"BigInt":{if(typeof e!="number"&&typeof e!="string")throw new W(`Expected a bigint in column '${t}', got ${typeof e}: ${e}`);return{$type:"BigInt",value:e}}case"Float":{if(typeof e=="number")return e;if(typeof e=="string"){let i=Number(e);if(Number.isNaN(i)&&!/^[-+]?nan$/.test(e.toLowerCase()))throw new W(`Expected a float in column '${t}', got string: ${e}`);return i}throw new W(`Expected a float in column '${t}', got ${typeof e}: ${e}`)}case"Boolean":{if(typeof e=="boolean")return e;if(typeof e=="number")return e===1;if(typeof e=="string"){if(e==="true"||e==="TRUE"||e==="1")return!0;if(e==="false"||e==="FALSE"||e==="0")return!1;throw new W(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}if(e instanceof Uint8Array){for(let i of e)if(i!==0)return!0;return!1}throw new W(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}case"Decimal":if(typeof e!="number"&&typeof e!="string"&&!ie.isDecimal(e))throw new W(`Expected a decimal in column '${t}', got ${typeof e}: ${e}`);return{$type:"Decimal",value:e};case"Date":{if(typeof e=="string")return{$type:"DateTime",value:Qa(e)};if(typeof e=="number"||e instanceof Date)return{$type:"DateTime",value:e};throw new W(`Expected a date in column '${t}', got ${typeof e}: ${e}`)}case"Time":{if(typeof e=="string")return{$type:"DateTime",value:`1970-01-01T${Qa(e)}`};throw new W(`Expected a time in column '${t}', got ${typeof e}: ${e}`)}case"Array":return e.map((o,s)=>Fi(o,`${t}[${s}]`,r.inner,n));case"Object":return{$type:"Json",value:typeof e=="string"?e:fr(e)};case"Bytes":{if(typeof e=="string"&&e.startsWith("\\x"))return{$type:"Bytes",value:y.from(e.slice(2),"hex").toString("base64")};if(Array.isArray(e))return{$type:"Bytes",value:y.from(e).toString("base64")};if(e instanceof Uint8Array)return{$type:"Bytes",value:y.from(e).toString("base64")};throw new W(`Expected a byte array in column '${t}', got ${typeof e}: ${e}`)}case"Enum":{let i=n[r.inner];if(i===void 0)throw new W(`Unknown enum '${r.inner}'`);let o=i[`${e}`];if(o===void 0)throw new W(`Unknown enum value '${e}' for enum '${r.inner}'`);return o}default:Q(r,`DataMapper: Unknown result type: ${r.type}`)}}var vm=/Z$|(?<!\d{4}-\d{2})[+-]\d{2}(:?\d{2})?$/;function Qa(e){let t=vm.exec(e);return t===null?`${e}Z`:t[0]!=="Z"&&t[1]===void 0?`${e}:00`:e}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var gr;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(gr||(gr={}));function Am(e){switch(e){case"postgresql":case"postgres":case"prisma+postgres":return"postgresql";case"sqlserver":return"mssql";case"mysql":case"sqlite":case"cockroachdb":case"mongodb":return e;default:Q(e,`Unknown provider: ${e}`)}}async function In({query:e,tracingHelper:t,provider:r,onQuery:n,execute:i}){return await t.runInChildSpan({name:"db_query",kind:gr.CLIENT,attributes:{"db.query.text":e.sql,"db.system.name":Am(r)}},async()=>{let o=new Date,s=b.now(),a=await i(),f=b.now();return n?.({timestamp:o,duration:f-s,query:e.sql,params:e.args}),a})}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();function Vi(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}c();u();p();m();d();l();var _={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Character:6,Text:7,Date:8,Time:9,DateTime:10,Json:11,Enum:12,Bytes:13,Set:14,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,EnumArray:76,BytesArray:77,UuidArray:78,UnknownNumber:128};var De=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n??{}}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function Ga(e){if(!Vi(e))throw e;let t=Cm(e),r=Rm(e);throw!t||!r?e:new De(r,t,{driverAdapterError:e})}function Cm(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"TransactionAlreadyClosed":return"P1018";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"ValueOutOfRange":return"P2020";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":case"InconsistentColumnData":return"P2023";case"MissingFullTextSearchIndex":return"P2030";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":case"mssql":return;default:Q(e.cause,`Unknown error: ${e.cause}`)}}function Rm(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"TransactionAlreadyClosed":return e.cause.cause;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${$i(e.cause.constraint)}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${$i(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${$i(e.cause.constraint)}`;case"ValueOutOfRange":return`Value out of range for the type: ${e.cause.cause}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Invalid isolation level \`${e.cause.level}\``;case"InconsistentColumnData":return`Inconsistent column data: ${e.cause.cause}`;case"MissingFullTextSearchIndex":return"Cannot find a fulltext index to use for the native search, try adding a @@fulltext([Fields...]) to your schema";case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":case"mssql":return;default:Q(e.cause,`Unknown error: ${e.cause}`)}}function $i(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();function et(e,t){var r="000000000"+e;return r.substr(r.length-t)}var Wa=Ae(cs(),1);function Sm(){try{return Wa.default.hostname()}catch{return g.env._CLUSTER_NETWORK_NAME_||g.env.COMPUTERNAME||"hostname"}}var Ja=2,Im=et(g.pid.toString(36),Ja),Ka=Sm(),km=Ka.length,Om=et(Ka.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+km+36).toString(36),Ja);function qi(){return Im+Om}c();u();p();m();d();l();c();u();p();m();d();l();function kn(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function Bi(e){let n=Math.pow(36,4),i=0;function o(){return et((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function a(){var f="c",w=new Date().getTime().toString(36),v=et(s().toString(36),4),A=e(),R=o()+o();return f+w+v+A+R}return a.fingerprint=e,a.isCuid=kn,a}var Dm=Bi(qi);var za=Dm;var Wl=Ae($l());c();u();p();m();d();l();Ye();c();u();p();m();d();l();var ql="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var wd=128,rt,kt;function bd(e){!rt||rt.length<e?(rt=y.allocUnsafe(e*wd),Gt.getRandomValues(rt),kt=0):kt+e>rt.length&&(Gt.getRandomValues(rt),kt=0),kt+=e}function Yi(e=21){bd(e|=0);let t="";for(let r=kt-e;r<kt;r++)t+=ql[rt[r]&63];return t}c();u();p();m();d();l();Ye();var jl="0123456789ABCDEFGHJKMNPQRSTVWXYZ",xr=32;var Ed=16,Ql=10,Bl=0xffffffffffff;var nt;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(nt||(nt={}));var it=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function xd(e){let t=Math.floor(e()*xr);return t===xr&&(t=xr-1),jl.charAt(t)}function Pd(e){let t=Td(),r=t&&(t.crypto||t.msCrypto)||(typeof ft<"u"?ft:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(ft?.randomBytes)return()=>ft.randomBytes(1).readUInt8()/255;throw new it(nt.PRNGDetectFailure,"Failed to find a reliable PRNG")}function Td(){return Cd()?self:typeof window<"u"?window:typeof globalThis<"u"||typeof globalThis<"u"?globalThis:null}function vd(e,t){let r="";for(;e>0;e--)r=xd(t)+r;return r}function Ad(e,t=Ql){if(isNaN(e))throw new it(nt.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>Bl)throw new it(nt.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${Bl}: ${e}`);if(e<0)throw new it(nt.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new it(nt.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%xr,n=jl.charAt(r)+n,e=(e-r)/xr;return n}function Cd(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function Hl(e,t){let r=t||Pd(),n=!e||isNaN(e)?Date.now():e;return Ad(n,Ql)+vd(Ed,r)}c();u();p();m();d();l();c();u();p();m();d();l();var re=[];for(let e=0;e<256;++e)re.push((e+256).toString(16).slice(1));function Mn(e,t=0){return(re[e[t+0]]+re[e[t+1]]+re[e[t+2]]+re[e[t+3]]+"-"+re[e[t+4]]+re[e[t+5]]+"-"+re[e[t+6]]+re[e[t+7]]+"-"+re[e[t+8]]+re[e[t+9]]+"-"+re[e[t+10]]+re[e[t+11]]+re[e[t+12]]+re[e[t+13]]+re[e[t+14]]+re[e[t+15]]).toLowerCase()}c();u();p();m();d();l();Ye();var Nn=new Uint8Array(256),Ln=Nn.length;function Ot(){return Ln>Nn.length-16&&(en(Nn),Ln=0),Nn.slice(Ln,Ln+=16)}c();u();p();m();d();l();c();u();p();m();d();l();Ye();var Zi={randomUUID:Xr};function Rd(e,t,r){if(Zi.randomUUID&&!t&&!e)return Zi.randomUUID();e=e||{};let n=e.random??e.rng?.()??Ot();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return Mn(n)}var Xi=Rd;c();u();p();m();d();l();var eo={};function Sd(e,t,r){let n;if(e)n=Gl(e.random??e.rng?.()??Ot(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=Ot();Id(eo,i,o),n=Gl(o,eo.msecs,eo.seq,t,r)}return t??Mn(n)}function Id(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function Gl(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var to=Sd;var Un=class{#e={};constructor(){this.register("uuid",new io),this.register("cuid",new oo),this.register("ulid",new so),this.register("nanoid",new ao),this.register("product",new lo)}snapshot(t){return Object.create(this.#e,{now:{value:t==="mysql"?new no:new ro}})}register(t,r){this.#e[t]=r}},ro=class{#e=new Date;generate(){return this.#e.toISOString()}},no=class{#e=new Date;generate(){return this.#e.toISOString().replace("T"," ").replace("Z","")}},io=class{generate(t){if(t===4)return Xi();if(t===7)return to();throw new Error("Invalid UUID generator arguments")}},oo=class{generate(t){if(t===1)return za();if(t===2)return(0,Wl.createId)();throw new Error("Invalid CUID generator arguments")}},so=class{generate(){return Hl()}},ao=class{generate(t){if(typeof t=="number")return Yi(t);if(t===void 0)return Yi();throw new Error("Invalid Nanoid generator arguments")}},lo=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};c();u();p();m();d();l();c();u();p();m();d();l();function co(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function uo(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function Jl(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bytes"}function Kl(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bigint"}function mo(e,t,r){let n=e.type;switch(n){case"rawSql":return Yl(e.sql,zl(e.params,t,r));case"templateSql":return kd(e.fragments,e.placeholderFormat,zl(e.params,t,r));default:Q(n,"Invalid query type")}}function zl(e,t,r){return e.map(n=>Te(n,t,r))}function Te(e,t,r){let n=e;for(;Dd(n);)if(co(n)){let i=t[n.prisma__value.name];if(i===void 0)throw new Error(`Missing value for query variable ${n.prisma__value.name}`);n=i}else if(uo(n)){let{name:i,args:o}=n.prisma__value,s=r[i];if(!s)throw new Error(`Encountered an unknown generator '${i}'`);n=s.generate(...o.map(a=>Te(a,t,r)))}else Q(n,`Unexpected unevaluated value type: ${n}`);return Array.isArray(n)?n=n.map(i=>Te(i,t,r)):Jl(n)?n=y.from(n.prisma__value,"base64"):Kl(n)&&(n=BigInt(n.prisma__value)),n}function kd(e,t,r){let n=0,i=1,o=[],s=e.map(a=>{let f=a.type;switch(f){case"parameter":if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);return o.push(r[n++]),po(t,i++);case"stringChunk":return a.chunk;case"parameterTuple":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let w=r[n++],v=Array.isArray(w)?w:[w];return`(${v.length==0?"NULL":v.map(R=>(o.push(R),po(t,i++))).join(",")})`}case"parameterTupleList":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let w=r[n++];if(!Array.isArray(w))throw new Error("Malformed query template. Tuple list expected.");if(w.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");return w.map(A=>{if(!Array.isArray(A))throw new Error("Malformed query template. Tuple expected.");let R=A.map(C=>(o.push(C),po(t,i++))).join(a.itemSeparator);return`${a.itemPrefix}${R}${a.itemSuffix}`}).join(a.groupSeparator)}default:Q(f,"Invalid fragment type")}}).join("");return Yl(s,o)}function po(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function Yl(e,t){let r=t.map(n=>Od(n));return{sql:e,args:t,argTypes:r}}function Od(e){return typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":y.isBuffer(e)?"Bytes":"Unknown"}function Dd(e){return co(e)||uo(e)}c();u();p();m();d();l();function Xl(e){let t=e.columnTypes.map(r=>{switch(r){case _.Bytes:return n=>Array.isArray(n)?new Uint8Array(n):n;default:return n=>n}});return e.rows.map(r=>r.map((n,i)=>t[i](n)).reduce((n,i,o)=>{let s=e.columnNames[o].split("."),a=n;for(let f=0;f<s.length;f++){let w=s[f];f===s.length-1?a[w]=i:(a[w]===void 0&&(a[w]={}),a=a[w])}return n},{}))}function ec(e){let r=e.columnTypes.map(n=>Zl(n)).map(n=>{switch(n){case"bytes":return i=>Array.isArray(i)?new Uint8Array(i):i;case"int":return i=>i===null?null:typeof i=="number"?i:parseInt(`${i}`,10);case"bigint":return i=>i===null?null:typeof i=="bigint"?i:BigInt(`${i}`);case"json":return i=>typeof i=="string"?JSON.parse(i):i;case"bool":return i=>typeof i=="string"?i==="true"||i==="1":typeof i=="number"?i===1:i;default:return i=>i}});return{columns:e.columnNames,types:e.columnTypes.map(n=>Zl(n)),rows:e.rows.map(n=>n.map((i,o)=>r[o](i)))}}function Zl(e){switch(e){case _.Int32:return"int";case _.Int64:return"bigint";case _.Float:return"float";case _.Double:return"double";case _.Text:return"string";case _.Enum:return"enum";case _.Bytes:return"bytes";case _.Boolean:return"bool";case _.Character:return"char";case _.Numeric:return"decimal";case _.Json:return"json";case _.Uuid:return"uuid";case _.DateTime:return"datetime";case _.Date:return"date";case _.Time:return"time";case _.Int32Array:return"int-array";case _.Int64Array:return"bigint-array";case _.FloatArray:return"float-array";case _.DoubleArray:return"double-array";case _.TextArray:return"string-array";case _.EnumArray:return"string-array";case _.BytesArray:return"bytes-array";case _.BooleanArray:return"bool-array";case _.CharacterArray:return"char-array";case _.NumericArray:return"decimal-array";case _.JsonArray:return"json-array";case _.UuidArray:return"uuid-array";case _.DateTimeArray:return"datetime-array";case _.DateArray:return"date-array";case _.TimeArray:return"time-array";case _.UnknownNumber:return"unknown";case _.Set:return"string";default:Q(e,`Unexpected column type: ${e}`)}}c();u();p();m();d();l();function tc(e,t,r){if(!t.every(n=>fo(e,n))){let n=_d(e,r),i=Md(r);throw new De(n,i,r.context)}}function fo(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"affectedRowCountEq":return e===t.args;case"never":return!1;default:Q(t,`Unknown rule type: ${t.type}`)}}function _d(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:e}.`;case"INCOMPLETE_CONNECT_OUTPUT":return`The required connected records were not found. Expected ${t.context.expectedRows} records to be connected after connect operation on ${t.context.relationType} relation '${t.context.relation}', found ${Array.isArray(e)?e.length:e}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:Q(t,`Unknown error identifier: ${t}`)}}function Md(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"INCOMPLETE_CONNECT_OUTPUT":return"P2018";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:Q(e,`Unknown error identifier: ${e}`)}}var Tr=class e{#e;#t;#r;#n=new Un;#o;#i;#s;#a;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o,rawSerializer:s,provider:a}){this.#e=t,this.#t=r,this.#r=n,this.#o=i,this.#i=o,this.#s=s??o,this.#a=a}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:Xl,rawSerializer:ec,provider:t.provider})}async run(t,r){let{value:n}=await this.interpretNode(t,r,this.#t,this.#n.snapshot(r.provider)).catch(i=>Ga(i));return n}async interpretNode(t,r,n,i){switch(t.type){case"value":return{value:Te(t.args,n,i)};case"seq":{let o;for(let s of t.args)o=await this.interpretNode(s,r,n,i);return o??{value:void 0}}case"get":return{value:n[t.args.name]};case"let":{let o=Object.create(n);for(let s of t.args.bindings){let{value:a}=await this.interpretNode(s.expr,r,o,i);o[s.name]=a}return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!rc(s))return{value:s}}return{value:[]}}case"concat":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>s.concat(Pr(a)),[]):[]}}case"sum":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>_e(s)+_e(a)):0}}case"execute":{let o=mo(t.args,n,i);return this.#l(o,r,async()=>({value:await r.executeRaw(o)}))}case"query":{let o=mo(t.args,n,i);return this.#l(o,r,async()=>{let s=await r.queryRaw(o);return t.args.type==="rawSql"?{value:this.#s(s),lastInsertId:s.lastInsertId}:{value:this.#i(s),lastInsertId:s.lastInsertId}})}case"reverse":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);return{value:Array.isArray(o)?o.reverse():o,lastInsertId:s}}case"unique":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return{value:o,lastInsertId:s};if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return{value:o[0]??null,lastInsertId:s}}case"required":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(rc(o))throw new Error("Required value is empty");return{value:o,lastInsertId:s}}case"mapField":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.records,r,n,i);return{value:ic(o,t.args.field),lastInsertId:s}}case"join":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.parent,r,n,i);if(o===null)return{value:null,lastInsertId:s};let a=await Promise.all(t.args.children.map(async f=>({joinExpr:f,childRecords:(await this.interpretNode(f.child,r,n,i)).value})));return{value:Ld(o,a),lastInsertId:s}}case"transaction":{if(!this.#e.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#e.manager,s=await o.startTransaction(),a=o.getTransaction(s,"query");try{let f=await this.interpretNode(t.args,a,n,i);return await o.commitTransaction(s.id),f}catch(f){throw await o.rollbackTransaction(s.id),f}}case"dataMap":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:Ha(o,t.args.structure,t.args.enums),lastInsertId:s}}case"validate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return tc(o,t.args.rules,t.args),{value:o,lastInsertId:s}}case"if":{let{value:o}=await this.interpretNode(t.args.value,r,n,i);return fo(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return{value:void 0};case"diff":{let{value:o}=await this.interpretNode(t.args.from,r,n,i),{value:s}=await this.interpretNode(t.args.to,r,n,i),a=new Set(Pr(s));return{value:Pr(o).filter(f=>!a.has(f))}}case"distinctBy":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=new Set,f=[];for(let w of Pr(o)){let v=Fn(w,t.args.fields);a.has(v)||(a.add(v),f.push(w))}return{value:f,lastInsertId:s}}case"paginate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=Pr(o),f=t.args.pagination.linkingFields;if(f!==null){let w=new Map;for(let A of a){let R=Fn(A,f);w.has(R)||w.set(R,[]),w.get(R).push(A)}let v=Array.from(w.entries());return v.sort(([A],[R])=>A<R?-1:A>R?1:0),{value:v.flatMap(([,A])=>nc(A,t.args.pagination)),lastInsertId:s}}return{value:nc(a,t.args.pagination),lastInsertId:s}}case"initializeRecord":{let{lastInsertId:o}=await this.interpretNode(t.args.expr,r,n,i),s={};for(let[a,f]of Object.entries(t.args.fields))s[a]=Nd(f,o,n,i);return{value:s,lastInsertId:o}}case"mapRecord":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=o===null?{}:go(o);for(let[f,w]of Object.entries(t.args.fields))a[f]=Ud(w,a[f],n,i);return{value:a,lastInsertId:s}}default:Q(t,`Unexpected node type: ${t.type}`)}}#l(t,r,n){return In({query:t,execute:n,provider:this.#a??r.provider,tracingHelper:this.#o,onQuery:this.#r})}};function rc(e){return Array.isArray(e)?e.length===0:e==null}function Pr(e){return Array.isArray(e)?e:[e]}function _e(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function go(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function ic(e,t){return Array.isArray(e)?e.map(r=>ic(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function Ld(e,t){for(let{joinExpr:r,childRecords:n}of t){let i=r.on.map(([a])=>a),o=r.on.map(([,a])=>a),s={};for(let a of Array.isArray(e)?e:[e]){let f=go(a),w=Fn(f,i);s[w]||(s[w]=[]),s[w].push(f),r.isRelationUnique?f[r.parentField]=null:f[r.parentField]=[]}for(let a of Array.isArray(n)?n:[n]){if(a===null)continue;let f=Fn(go(a),o);for(let w of s[f]??[])r.isRelationUnique?w[r.parentField]=a:w[r.parentField].push(a)}}return e}function nc(e,{cursor:t,skip:r,take:n}){let i=t!==null?e.findIndex(a=>dr(a,t)):0;if(i===-1)return[];let o=i+(r??0),s=n!==null?o+n:e.length;return e.slice(o,s)}function Fn(e,t){return JSON.stringify(t.map(r=>e[r]))}function Nd(e,t,r,n){switch(e.type){case"value":return Te(e.value,r,n);case"lastInsertId":return t;default:Q(e,`Unexpected field initializer type: ${e.type}`)}}function Ud(e,t,r,n){switch(e.type){case"set":return Te(e.value,r,n);case"add":return _e(t)+_e(Te(e.value,r,n));case"subtract":return _e(t)-_e(Te(e.value,r,n));case"multiply":return _e(t)*_e(Te(e.value,r,n));case"divide":{let i=_e(t),o=_e(Te(e.value,r,n));return o===0?null:i/o}default:Q(e,`Unexpected field operation type: ${e.type}`)}}c();u();p();m();d();l();c();u();p();m();d();l();async function Fd(){return globalThis.crypto??await Promise.resolve().then(()=>(Ye(),ui))}async function oc(){return(await Fd()).randomUUID()}c();u();p();m();d();l();var we=class extends De{name="TransactionManagerError";constructor(t,r){super("Transaction API error: "+t,"P2028",r)}},vr=class extends we{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},Vn=class extends we{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction.`)}},$n=class extends we{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back.`)}},qn=class extends we{constructor(){super("Unable to start a transaction in the given time.")}},Bn=class extends we{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction`,{operation:t,timeout:r,timeTaken:n})}},Dt=class extends we{constructor(t){super(`Internal Consistency Error: ${t}`)}},jn=class extends we{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var Vd=100,Ar=J("prisma:client:transactionManager"),$d=()=>({sql:"COMMIT",args:[],argTypes:[]}),qd=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),Bd=()=>({sql:'-- Implicit "COMMIT" query via underlying driver',args:[],argTypes:[]}),jd=()=>({sql:'-- Implicit "ROLLBACK" query via underlying driver',args:[],argTypes:[]}),Cr=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;#e;#t;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n,onQuery:i,provider:o}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n,this.#e=i,this.#t=o}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#r(t))}async#r(t){let r=t!==void 0?this.validateOptions(t):this.transactionOptions,n={id:await oc(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n);let i=setTimeout(()=>n.status="timed_out",r.maxWait);switch(n.transaction=await this.driverAdapter.startTransaction(r.isolationLevel),clearTimeout(i),n.status){case"waiting":return n.status="running",n.timer=this.startTransactionTimeout(n.id,r.timeout),{id:n.id};case"timed_out":throw await this.closeTransaction(n,"timed_out"),new qn;case"running":case"committed":case"rolled_back":throw new Dt(`Transaction in invalid state ${n.status} although it just finished startup.`);default:Q(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.getActiveTransaction(t,"commit");await this.closeTransaction(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.getActiveTransaction(t,"rollback");await this.closeTransaction(r,"rolled_back")})}getTransaction(t,r){let n=this.getActiveTransaction(t.id,r);if(!n.transaction)throw new vr;return n.transaction}getActiveTransaction(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(Ar("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"waiting":case"running":throw new Dt("Active transaction found in closed transactions list.");case"committed":throw new Vn(r);case"rolled_back":throw new $n(r);case"timed_out":throw new Bn(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw Ar("Transaction not found.",t),new vr}if(["committed","rolled_back","timed_out"].includes(n.status))throw new Dt("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.closeTransaction(t,"rolled_back")))}startTransactionTimeout(t,r){let n=Date.now();return setTimeout(async()=>{Ar("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let i=this.transactions.get(t);i&&["running","waiting"].includes(i.status)?await this.closeTransaction(i,"timed_out"):Ar("Transaction already committed or rolled back when timeout happened.",t)},r)}async closeTransaction(t,r){Ar("Closing transaction.",{transactionId:t.id,status:r}),t.status=r;try{if(t.transaction&&r==="committed")if(t.transaction.options.usePhantomQuery)await this.#n(Bd(),t.transaction,()=>t.transaction.commit());else{let n=$d();await this.#n(n,t.transaction,()=>t.transaction.executeRaw(n)),await t.transaction.commit()}else if(t.transaction)if(t.transaction.options.usePhantomQuery)await this.#n(jd(),t.transaction,()=>t.transaction.rollback());else{let n=qd();await this.#n(n,t.transaction,()=>t.transaction.executeRaw(n)),await t.transaction.rollback()}}finally{clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>Vd&&this.closedTransactions.shift()}}validateOptions(t){if(!t.timeout)throw new we("timeout is required");if(!t.maxWait)throw new we("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new jn(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}#n(t,r,n){return In({query:t,execute:n,provider:this.#t??r.provider,tracingHelper:this.tracingHelper,onQuery:this.#e})}};var Qn="6.12.0";c();u();p();m();d();l();var Hn=class e{#e;#t;#r;constructor(t,r,n){this.#e=t,this.#t=r,this.#r=n}static async connect(t){let r,n;try{r=await t.driverAdapterFactory.connect(),n=new Cr({driverAdapter:r,transactionOptions:t.transactionOptions,tracingHelper:t.tracingHelper,onQuery:t.onQuery,provider:t.provider})}catch(i){throw await r?.dispose(),i}return new e(t,r,n)}getConnectionInfo(){let t=this.#t.getConnectionInfo?.()??{supportsRelationJoins:!1};return Promise.resolve({provider:this.#t.provider,connectionInfo:t})}async execute({plan:t,placeholderValues:r,transaction:n,batchIndex:i}){let o=n?this.#r.getTransaction(n,i!==void 0?"batch query":"query"):this.#t;return await Tr.forSql({transactionManager:n?{enabled:!1}:{enabled:!0,manager:this.#r},placeholderValues:r,onQuery:this.#e.onQuery,tracingHelper:this.#e.tracingHelper,provider:this.#e.provider}).run(t,o)}async startTransaction(t){return{...await this.#r.startTransaction(t),payload:void 0}}async commitTransaction(t){await this.#r.commitTransaction(t.id)}async rollbackTransaction(t){await this.#r.rollbackTransaction(t.id)}async disconnect(){try{await this.#r.cancelAllTransactions()}finally{await this.#t.dispose()}}};c();u();p();m();d();l();c();u();p();m();d();l();var Gn=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function sc(e,t,r){let n=r||{},i=n.encode||encodeURIComponent;if(typeof i!="function")throw new TypeError("option encode is invalid");if(!Gn.test(e))throw new TypeError("argument name is invalid");let o=i(t);if(o&&!Gn.test(o))throw new TypeError("argument val is invalid");let s=e+"="+o;if(n.maxAge!==void 0&&n.maxAge!==null){let a=n.maxAge-0;if(Number.isNaN(a)||!Number.isFinite(a))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(a)}if(n.domain){if(!Gn.test(n.domain))throw new TypeError("option domain is invalid");s+="; Domain="+n.domain}if(n.path){if(!Gn.test(n.path))throw new TypeError("option path is invalid");s+="; Path="+n.path}if(n.expires){if(!Hd(n.expires)||Number.isNaN(n.expires.valueOf()))throw new TypeError("option expires is invalid");s+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(s+="; HttpOnly"),n.secure&&(s+="; Secure"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():n.priority){case"low":{s+="; Priority=Low";break}case"medium":{s+="; Priority=Medium";break}case"high":{s+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:{s+="; SameSite=Strict";break}case"lax":{s+="; SameSite=Lax";break}case"strict":{s+="; SameSite=Strict";break}case"none":{s+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return n.partitioned&&(s+="; Partitioned"),s}function Hd(e){return Object.prototype.toString.call(e)==="[object Date]"||e instanceof Date}function ac(e,t){let r=(e||"").split(";").filter(f=>typeof f=="string"&&!!f.trim()),n=r.shift()||"",i=Gd(n),o=i.name,s=i.value;try{s=t?.decode===!1?s:(t?.decode||decodeURIComponent)(s)}catch{}let a={name:o,value:s};for(let f of r){let w=f.split("="),v=(w.shift()||"").trimStart().toLowerCase(),A=w.join("=");switch(v){case"expires":{a.expires=new Date(A);break}case"max-age":{a.maxAge=Number.parseInt(A,10);break}case"secure":{a.secure=!0;break}case"httponly":{a.httpOnly=!0;break}case"samesite":{a.sameSite=A;break}default:a[v]=A}}return a}function Gd(e){let t="",r="",n=e.split("=");return n.length>1?(t=n.shift(),r=n.join("=")):r=e,{name:t,value:r}}c();u();p();m();d();l();c();u();p();m();d();l();function _t({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw mr().id==="workerd"?new U(`error: Environment variable not found: ${s.fromEnvVar}.

In Cloudflare module Workers, environment variables are available only in the Worker's \`env\` parameter of \`fetch\`.
To solve this, provide the connection string directly: https://pris.ly/d/cloudflare-datasource-url`,n):new U(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new U("error: Missing URL environment variable, value, or override.",n);return i}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var Wn=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var me=class extends Wn{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};c();u();p();m();d();l();function L(e,t){return{...e,isRetryable:t}}var ot=class extends me{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,L(r,!1))}};k(ot,"InvalidDatasourceError");function Jn(e){let t={clientVersion:e.clientVersion},r=Object.keys(e.inlineDatasources)[0],n=_t({inlineDatasources:e.inlineDatasources,overrideDatasources:e.overrideDatasources,clientVersion:e.clientVersion,env:{...e.env,...typeof g<"u"?g.env:{}}}),i;try{i=new URL(n)}catch{throw new ot(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==rn)throw new ot(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let a=s.get("api_key");if(a===null||a.length<1)throw new ot(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);let f=mi(i)?"http:":"https:",w=new URL(i.href.replace(o,f));return{apiKey:a,url:w}}c();u();p();m();d();l();var lc=Ae(ys()),Mt=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,transactionId:r}={}){let n={Accept:"application/json",Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json","Prisma-Engine-Hash":this.engineHash,"Prisma-Engine-Version":lc.enginesVersion};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-Transaction-Id"]=r);let i=this.#e();return i.length>0&&(n["X-Capture-Telemetry"]=i.join(", ")),n}#e(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}};c();u();p();m();d();l();function Wd(e){return e[0]*1e3+e[1]/1e6}function Lt(e){return new Date(Wd(e))}var cc=J("prisma:client:clientEngine:remoteExecutor"),Kn=class{#e;#t;#r;#n;#o;constructor(t){this.#e=t.clientVersion,this.#n=t.logEmitter,this.#o=t.tracingHelper;let{url:r,apiKey:n}=Jn({clientVersion:t.clientVersion,env:t.env,inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources});this.#r=new yo(r),this.#t=new Mt({apiKey:n,engineHash:t.clientVersion,logLevel:t.logLevel,logQueries:t.logQueries,tracingHelper:t.tracingHelper})}async getConnectionInfo(){return await this.#i({path:"/connection-info",method:"GET"})}async execute({plan:t,placeholderValues:r,batchIndex:n,model:i,operation:o,transaction:s,customFetch:a}){return(await this.#i({path:s?`/transaction/${s.id}/query`:"/query",method:"POST",body:{model:i,operation:o,plan:t,params:r},batchRequestIdx:n,fetch:a})).data}async startTransaction(t){return{...await this.#i({path:"/transaction/start",method:"POST",body:t}),payload:void 0}}async commitTransaction(t){await this.#i({path:`/transaction/${t.id}/commit`,method:"POST"})}async rollbackTransaction(t){await this.#i({path:`/transaction/${t.id}/rollback`,method:"POST"})}disconnect(){return Promise.resolve()}async#i({path:t,method:r,body:n,fetch:i=globalThis.fetch,batchRequestIdx:o}){let s=await this.#r.request({method:r,path:t,headers:this.#t.build(),body:n,fetch:i});s.ok||await this.#s(s,o);let a=await s.json();return typeof a.extensions=="object"&&a.extensions!==null&&this.#a(a.extensions),a}async#s(t,r){let n=t.headers.get("Prisma-Error-Code"),i=await t.text(),o,s=i;try{o=JSON.parse(i)}catch{o={}}typeof o.code=="string"&&(n=o.code),typeof o.error=="string"?s=o.error:typeof o.message=="string"?s=o.message:typeof o.InvalidRequestError=="object"&&o.InvalidRequestError!==null&&typeof o.InvalidRequestError.reason=="string"&&(s=o.InvalidRequestError.reason),s=s||`HTTP ${t.status}: ${t.statusText}`;let a=typeof o.meta=="object"&&o.meta!==null?o.meta:o;throw new Z(s,{clientVersion:this.#e,code:n??"P6000",batchRequestIdx:r,meta:a})}#a(t){if(t.logs)for(let r of t.logs)this.#l(r);t.traces&&this.#o.dispatchEngineSpans(t.traces)}#l(t){switch(t.level){case"debug":case"trace":cc(t);break;case"error":case"warn":case"info":{this.#n.emit(t.level,{timestamp:Lt(t.timestamp),message:t.attributes.message??"",target:t.target});break}case"query":{this.#n.emit("query",{query:t.attributes.query??"",timestamp:Lt(t.timestamp),duration:t.attributes.duration_ms??0,params:t.attributes.params??"",target:t.target});break}default:throw new Error(`Unexpected log level: ${t.level}`)}}},yo=class{#e;#t;#r;constructor(t){this.#e=t,this.#t=new Map}async request({method:t,path:r,headers:n,body:i,fetch:o}){let s=new URL(r,this.#e),a=this.#n(s);a&&(n.Cookie=a),this.#r&&(n["Accelerate-Query-Engine-Jwt"]=this.#r);let f=await o(s,{method:t,body:i!==void 0?JSON.stringify(i):void 0,headers:n});return cc(t,s,f.status,f.statusText),this.#r=f.headers.get("Accelerate-Query-Engine-Jwt")??void 0,this.#o(s,f),f}#n(t){let r=[],n=new Date;for(let[i,o]of this.#t){if(o.expires&&o.expires<n){this.#t.delete(i);continue}let s=o.domain??t.hostname,a=o.path??"/";t.hostname.endsWith(s)&&t.pathname.startsWith(a)&&r.push(sc(o.name,o.value))}return r.length>0?r.join("; "):void 0}#o(t,r){let n=r.headers.getSetCookie?.()||[];if(n.length===0){let i=r.headers.get("Set-Cookie");i&&n.push(i)}for(let i of n){let o=ac(i),s=o.domain??t.hostname,a=o.path??"/",f=`${s}:${a}:${o.name}`;this.#t.set(f,{name:o.name,value:o.value,domain:s,path:a,expires:o.expires})}}};c();u();p();m();d();l();var ho,uc={async loadQueryCompiler(e){let{clientVersion:t,compilerWasm:r}=e;if(r===void 0)throw new U("WASM query compiler was unexpectedly `undefined`",t);return ho===void 0&&(ho=(async()=>{let n=await r.getRuntime(),i=await r.getQueryCompilerWasmModule();if(i==null)throw new U("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let o={"./query_compiler_bg.js":n},s=new WebAssembly.Instance(i,o),a=s.exports.__wbindgen_start;return n.__wbg_set_wasm(s.exports),a(),n.QueryCompiler})()),await ho}};var pc="P2038",Rr=J("prisma:client:clientEngine"),dc=globalThis;dc.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new pe(e,Qn)}};var Sr=class{name="ClientEngine";#e;#t={type:"disconnected"};#r;#n;config;datamodel;logEmitter;logQueries;logLevel;tracingHelper;#o;constructor(t,r,n){if(!t.previewFeatures?.includes("driverAdapters")&&!r)throw new U("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,pc);if(r)this.#n={remote:!0};else if(t.adapter)this.#n={remote:!1,driverAdapterFactory:t.adapter},Rr("Using driver adapter: %O",t.adapter);else throw new U("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,pc);this.#r=n??uc,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#o=i=>{this.logEmitter.emit("query",{...i,params:fr(i.params),target:"ClientEngine"})})}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async#i(){switch(this.#t.type){case"disconnected":{let t=this.tracingHelper.runInChildSpan("connect",async()=>{let r,n;try{r=await this.#s(),n=await this.#a(r)}catch(o){throw this.#t={type:"disconnected"},n?.free(),await r?.disconnect(),o}let i={executor:r,queryCompiler:n};return this.#t={type:"connected",engine:i},i});return this.#t={type:"connecting",promise:t},await t}case"connecting":return await this.#t.promise;case"connected":return this.#t.engine;case"disconnecting":return await this.#t.promise,await this.#i()}}async#s(){return this.#n.remote?new Kn({clientVersion:this.config.clientVersion,env:this.config.env,inlineDatasources:this.config.inlineDatasources,logEmitter:this.logEmitter,logLevel:this.logLevel,logQueries:this.logQueries,overrideDatasources:this.config.overrideDatasources,tracingHelper:this.tracingHelper}):await Hn.connect({driverAdapterFactory:this.#n.driverAdapterFactory,tracingHelper:this.tracingHelper,transactionOptions:{...this.config.transactionOptions,isolationLevel:this.#m(this.config.transactionOptions.isolationLevel)},onQuery:this.#o,provider:this.config.activeProvider})}async#a(t){let r=this.#e;r===void 0&&(r=await this.#r.loadQueryCompiler(this.config),this.#e=r);let{provider:n,connectionInfo:i}=await t.getConnectionInfo();try{return this.#p(()=>new r({datamodel:this.datamodel,provider:n,connectionInfo:i}),void 0,!1)}catch(o){throw this.#l(o)}}#l(t){if(t instanceof pe)return t;try{let r=JSON.parse(t.message);return new U(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#c(t,r){if(t instanceof U)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new pe(mc(this,t.message,r),this.config.clientVersion);if(t instanceof De)return new Z(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let n=JSON.parse(t);return new se(`${n.message}
${n.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#u(t){return t instanceof pe?t:typeof t.message=="string"&&typeof t.code=="string"?new Z(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):t}#p(t,r,n=!0){let i=dc.PRISMA_WASM_PANIC_REGISTRY.set_message,o;globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=s=>{o=s};try{return t()}finally{if(globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=i,o)throw this.#e=void 0,n&&this.stop().catch(s=>Rr("failed to disconnect:",s)),new pe(mc(this,o,r),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.#i()}async stop(){switch(this.#t.type){case"disconnected":return;case"connecting":return await this.#t.promise,await this.stop();case"connected":{let t=this.#t.engine,r=this.tracingHelper.runInChildSpan("disconnect",async()=>{try{await t.executor.disconnect(),t.queryCompiler.free()}finally{this.#t={type:"disconnected"}}});return this.#t={type:"disconnecting",promise:r},await r}case"disconnecting":return await this.#t.promise}}version(){return"unknown"}async transaction(t,r,n){let i,{executor:o}=await this.#i();try{if(t==="start"){let s=n;i=await o.startTransaction({...s,isolationLevel:this.#m(s.isolationLevel)})}else if(t==="commit"){let s=n;await o.commitTransaction(s)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s)}else xe(t,"Invalid transaction action.")}catch(s){throw this.#c(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{interactiveTransaction:r,customDataProxyFetch:n}){Rr("sending request");let i=JSON.stringify(t),{executor:o,queryCompiler:s}=await this.#i().catch(f=>{throw this.#c(f,i)}),a;try{a=this.#p(()=>s.compile(i),i)}catch(f){throw this.#u(f)}try{Rr("query plan created",a);let f={},w=await o.execute({plan:a,model:t.modelName,operation:t.action,placeholderValues:f,transaction:r,batchIndex:void 0,customFetch:n?.(globalThis.fetch)});return Rr("query plan executed"),{data:{[t.action]:w}}}catch(f){throw this.#c(f,i)}}async requestBatch(t,{transaction:r,customDataProxyFetch:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(Ct(t,r)),{executor:s,queryCompiler:a}=await this.#i().catch(w=>{throw this.#c(w,o)}),f;try{f=a.compileBatch(o)}catch(w){throw this.#u(w)}try{let w;if(r?.kind==="itx")w=r.options;else{let R=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;w=await this.transaction("start",{},R)}let v={},A=[];switch(f.type){case"multi":{A=await Promise.all(f.plans.map((R,C)=>s.execute({plan:R,placeholderValues:v,model:t[C].modelName,operation:t[C].action,batchIndex:C,transaction:w,customFetch:n?.(globalThis.fetch)}).then(D=>({data:{[t[C].action]:D}}),D=>D)));break}case"compacted":{if(!t.every(C=>C.action===i))throw new Error("All queries in a batch must have the same action");let R=await s.execute({plan:f.plan,placeholderValues:v,model:t[0].modelName,operation:i,batchIndex:void 0,transaction:w,customFetch:n?.(globalThis.fetch)});A=this.#d(R,f,i);break}}return r?.kind!=="itx"&&await this.transaction("commit",{},w),A}catch(w){throw this.#c(w,o)}}metrics(t){throw new Error("Method not implemented.")}#d(t,r,n){let i=t.map(s=>r.keys.reduce((a,f)=>(a[f]=Ze(s[f]),a),{})),o=new Set(r.nestedSelection);return r.arguments.map(s=>{let a=i.findIndex(f=>dr(f,s));if(a===-1)return r.expectNonEmpty?new Z("An operation failed because it depends on one or more records that were required but not found",{code:"P2025",clientVersion:this.config.clientVersion}):{data:{[n]:null}};{let f=Object.entries(t[a]).filter(([w])=>o.has(w));return{data:{[n]:Object.fromEntries(f)}}}})}#m(t){switch(t){case void 0:return;case"ReadUncommitted":return"READ UNCOMMITTED";case"ReadCommitted":return"READ COMMITTED";case"RepeatableRead":return"REPEATABLE READ";case"Serializable":return"SERIALIZABLE";case"Snapshot":return"SNAPSHOT";default:throw new Z(`Inconsistent column data: Conversion failed: Invalid isolation level \`${t}\``,{code:"P2023",clientVersion:this.config.clientVersion,meta:{providedIsolationLevel:t}})}}};function mc(e,t,r){return Ua({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:r})}c();u();p();m();d();l();c();u();p();m();d();l();var Nt=class extends me{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",L(t,!0))}};k(Nt,"ForcedRetryError");c();u();p();m();d();l();var st=class extends me{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,L(r,!1))}};k(st,"NotImplementedYetError");c();u();p();m();d();l();c();u();p();m();d();l();var B=class extends me{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var at=class extends B{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",L(t,!0))}};k(at,"SchemaMissingError");c();u();p();m();d();l();c();u();p();m();d();l();var wo="This request could not be understood by the server",Ir=class extends B{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||wo,L(t,!1)),n&&(this.code=n)}};k(Ir,"BadRequestError");c();u();p();m();d();l();var kr=class extends B{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",L(t,!0)),this.logs=r}};k(kr,"HealthcheckTimeoutError");c();u();p();m();d();l();var Or=class extends B{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,L(t,!0)),this.logs=n}};k(Or,"EngineStartupError");c();u();p();m();d();l();var Dr=class extends B{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",L(t,!1))}};k(Dr,"EngineVersionNotSupportedError");c();u();p();m();d();l();var bo="Request timed out",_r=class extends B{name="GatewayTimeoutError";code="P5009";constructor(t,r=bo){super(r,L(t,!1))}};k(_r,"GatewayTimeoutError");c();u();p();m();d();l();var Jd="Interactive transaction error",Mr=class extends B{name="InteractiveTransactionError";code="P5015";constructor(t,r=Jd){super(r,L(t,!1))}};k(Mr,"InteractiveTransactionError");c();u();p();m();d();l();var Kd="Request parameters are invalid",Lr=class extends B{name="InvalidRequestError";code="P5011";constructor(t,r=Kd){super(r,L(t,!1))}};k(Lr,"InvalidRequestError");c();u();p();m();d();l();var Eo="Requested resource does not exist",Nr=class extends B{name="NotFoundError";code="P5003";constructor(t,r=Eo){super(r,L(t,!1))}};k(Nr,"NotFoundError");c();u();p();m();d();l();var xo="Unknown server error",Ut=class extends B{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||xo,L(t,!0)),this.logs=n}};k(Ut,"ServerError");c();u();p();m();d();l();var Po="Unauthorized, check your connection string",Ur=class extends B{name="UnauthorizedError";code="P5007";constructor(t,r=Po){super(r,L(t,!1))}};k(Ur,"UnauthorizedError");c();u();p();m();d();l();var To="Usage exceeded, retry again later",Fr=class extends B{name="UsageExceededError";code="P5008";constructor(t,r=To){super(r,L(t,!0))}};k(Fr,"UsageExceededError");async function zd(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function Vr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await zd(e);if(n.type==="QueryEngineError")throw new Z(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new Ut(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new at(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Dr(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Or(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new U(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new kr(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new Mr(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Lr(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Ur(r,Ft(Po,n));if(e.status===404)return new Nr(r,Ft(Eo,n));if(e.status===429)throw new Fr(r,Ft(To,n));if(e.status===504)throw new _r(r,Ft(bo,n));if(e.status>=500)throw new Ut(r,Ft(xo,n));if(e.status>=400)throw new Ir(r,Ft(wo,n))}function Ft(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}c();u();p();m();d();l();function fc(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}c();u();p();m();d();l();var Fe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function gc(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,f,w,v;for(let A=0;A<o;A=A+3)v=t[A]<<16|t[A+1]<<8|t[A+2],s=(v&16515072)>>18,a=(v&258048)>>12,f=(v&4032)>>6,w=v&63,r+=Fe[s]+Fe[a]+Fe[f]+Fe[w];return i==1?(v=t[o],s=(v&252)>>2,a=(v&3)<<4,r+=Fe[s]+Fe[a]+"=="):i==2&&(v=t[o]<<8|t[o+1],s=(v&64512)>>10,a=(v&1008)>>4,f=(v&15)<<2,r+=Fe[s]+Fe[a]+Fe[f]+"="),r}c();u();p();m();d();l();function yc(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new U("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}c();u();p();m();d();l();var hc={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};c();u();p();m();d();l();c();u();p();m();d();l();var $r=class extends me{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,L(r,!0))}};k($r,"RequestError");async function lt(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new $r(a,{clientVersion:n,cause:s})}}var Zd=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,wc=J("prisma:client:dataproxyEngine");async function Xd(e,t){let r=hc["@prisma/engines-version"],n=t.clientVersion??"unknown";if(g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&Zd.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[a,f,w]=s.split("."),v=ef(`<=${a}.${f}.${w}`),A=await lt(v,{clientVersion:n});if(!A.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${A.status} ${A.statusText}, response body: ${await A.text()||"<empty body>"}`);let R=await A.text();wc("length of body fetched from unpkg.com",R.length);let C;try{C=JSON.parse(R)}catch(D){throw console.error("JSON.parse error: body fetched from unpkg.com: ",R),D}return C.version}throw new st("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function bc(e,t){let r=await Xd(e,t);return wc("version",r),r}function ef(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var Ec=3,qr=J("prisma:client:dataproxyEngine"),Br=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){yc(t),this.config=t,this.env=t.env,this.inlineSchema=gc(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.protocol=r.protocol,this.headerBuilder=new Mt({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel??"error",logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await bc(this.host,this.config),qr("host",this.host),qr("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":qr(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:Lt(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:Lt(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}//${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await lt(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||qr("schema response status",r.status);let n=await Vr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=Ct(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(f=>(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f?this.convertProtocolErrorsToClientError(f.errors):f))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await lt(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,transactionId:i?.id}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||qr("graphql response status",a.status),await this.handleError(await Vr(a,this.clientVersion));let f=await a.json();if(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f)throw this.convertProtocolErrorsToClientError(f.errors);return"batchResult"in f?f.batchResult:f}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let f=await lt(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await Vr(f,this.clientVersion));let w=await f.json(),{extensions:v}=w;v&&this.propagateResponseExtensions(v);let A=w.id,R=w["data-proxy"].endpoint;return{id:A,payload:{endpoint:R}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await lt(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await Vr(a,this.clientVersion));let f=await a.json(),{extensions:w}=f;w&&this.propagateResponseExtensions(w);return}}})}getURLAndAPIKey(){return Jn({clientVersion:this.clientVersion,env:this.env,inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources})}metrics(){throw new st("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof me)||!i.isRetryable)throw i;if(r>=Ec)throw i instanceof Nt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${Ec} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await fc(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof at)throw await this.uploadSchema(),new Nt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?Cn(t[0],this.config.clientVersion,this.config.activeProvider):new se(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};c();u();p();m();d();l();function xc({url:e,adapter:t,copyEngine:r,targetBuildType:n}){let i=[],o=[],s=I=>{i.push({_tag:"warning",value:I})},a=I=>{let M=I.join(`
`);o.push({_tag:"error",value:M})},f=!!e?.startsWith("prisma://"),w=nn(e),v=!!t,A=f||w;!v&&r&&A&&s(["recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"]);let R=A||!r;v&&(R||n==="edge")&&(n==="edge"?a(["Prisma Client was configured to use the `adapter` option but it was imported via its `/edge` endpoint.","Please either remove the `/edge` endpoint or remove the `adapter` from the Prisma Client constructor."]):r?f&&a(["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]):a(["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]));let C={accelerate:R,ppg:w,driverAdapters:v};function D(I){return I.length>0}return D(o)?{ok:!1,diagnostics:{warnings:i,errors:o},isUsing:C}:{ok:!0,diagnostics:{warnings:i},isUsing:C}}function Pc({copyEngine:e=!0},t){let r;try{r=_t({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...g.env},clientVersion:t.clientVersion})}catch{}let{ok:n,isUsing:i,diagnostics:o}=xc({url:r,adapter:t.adapter,copyEngine:e,targetBuildType:"wasm-compiler-edge"});for(let A of o.warnings)sn(...A.value);if(!n){let A=o.errors[0];throw new oe(A.value,{clientVersion:t.clientVersion})}let s=dt(t.generator),a=s==="library",f=s==="binary",w=s==="client",v=(i.accelerate||i.ppg)&&!i.driverAdapters;if(w)return new Sr(t,v);if(i.accelerate)return new Br(t);i.driverAdapters,i.accelerate;{let A=[`PrismaClient failed to initialize because it wasn't configured to run in this environment (${mr().prettyName}).`,"In order to run Prisma Client in an edge runtime, you will need to configure one of the following options:","- Enable Driver Adapters: https://pris.ly/d/driver-adapters","- Enable Accelerate: https://pris.ly/d/accelerate"];throw new oe(A.join(`
`),{clientVersion:t.clientVersion})}return"wasm-compiler-edge"}c();u();p();m();d();l();function zn({generator:e}){return e?.previewFeatures??[]}c();u();p();m();d();l();var Tc=e=>({command:e});c();u();p();m();d();l();c();u();p();m();d();l();var vc=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);c();u();p();m();d();l();l();function Vt(e){try{return Ac(e,"fast")}catch{return Ac(e,"slow")}}function Ac(e,t){return JSON.stringify(e.map(r=>Rc(r,t)))}function Rc(e,t){if(Array.isArray(e))return e.map(r=>Rc(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(yt(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(ie.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(y.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(tf(e))return{prisma__type:"bytes",prisma__value:y.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:y.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?Sc(e):e}function tf(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Sc(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(Cc);let t={};for(let r of Object.keys(e))t[r]=Cc(e[r]);return t}function Cc(e){return typeof e=="bigint"?e.toString():Sc(e)}var rf=/^(\s*alter\s)/i,Ic=J("prisma:client");function vo(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&rf.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var Ao=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(Pn(r))n=r.sql,i={values:Vt(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:Vt(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:Vt(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:Vt(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=vc(r),i={values:Vt(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?Ic(`prisma.${e}(${n}, ${i.values})`):Ic(`prisma.${e}(${n})`),{query:n,parameters:i}},kc={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new ye(t,r)}},Oc={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};c();u();p();m();d();l();function Co(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=Dc(r(s)):Dc(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function Dc(e){return typeof e.then=="function"?e:Promise.resolve(e)}c();u();p();m();d();l();var nf=ci.split(".")[0],of={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},Ro=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${nf}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??of}};function _c(){return new Ro}c();u();p();m();d();l();function Mc(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}c();u();p();m();d();l();function Lc(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}c();u();p();m();d();l();var Yn=class{_middlewares=[];use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};c();u();p();m();d();l();var Uc=Ae(fi());c();u();p();m();d();l();function Zn(e){return typeof e.batchRequestIdx=="number"}c();u();p();m();d();l();function Nc(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(So(e.query.arguments)),t.push(So(e.query.selection)),t.join("")}function So(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${So(n)})`:r}).join(" ")})`}c();u();p();m();d();l();var sf={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Io(e){return sf[e]}c();u();p();m();d();l();var Xn=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,g.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};c();u();p();m();d();l();l();function ct(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new ie(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>ct("bigint",r));case"bytes-array":return t.map(r=>ct("bytes",r));case"decimal-array":return t.map(r=>ct("decimal",r));case"datetime-array":return t.map(r=>ct("datetime",r));case"date-array":return t.map(r=>ct("date",r));case"time-array":return t.map(r=>ct("time",r));default:return t}}function ko(e){let t=[],r=af(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=ct(e.types[s],i[s]);t.push(o)}return t}function af(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var lf=J("prisma:client:request_handler"),ei=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new Xn({batchLoader:Ca(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(A=>A.protocolQuery),f=this.client._tracingHelper.getTraceParent(s),w=n.some(A=>Io(A.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:f,transaction:cf(o),containsWrite:w,customDataProxyFetch:i})).map((A,R)=>{if(A instanceof Error)return A;try{return this.mapQueryEngineResult(n[R],A)}catch(C){return C}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Fc(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Io(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:Nc(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return g.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(lf(t),uf(t,i))throw t;if(t instanceof Z&&pf(t)){let w=Vc(t.meta);wn({args:o,errors:[w],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let f=t.message;if(n&&(f=cn({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:f})),f=this.sanitizeMessage(f),t.code){let w=s?{modelName:s,...t.meta}:t.meta;throw new Z(f,{code:t.code,clientVersion:this.client._clientVersion,meta:w,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new pe(f,this.client._clientVersion);if(t instanceof se)throw new se(f,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof U)throw new U(f,this.client._clientVersion);if(t instanceof pe)throw new pe(f,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,Uc.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(w=>w!=="select"&&w!=="include"),a=Oi(o,s),f=i==="queryRaw"?ko(a):Ze(a);return n?n(f):f}get[Symbol.toStringTag](){return"RequestHandler"}};function cf(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Fc(e)};xe(e,"Unknown transaction kind")}}function Fc(e){return{id:e.id,payload:e.payload}}function uf(e,t){return Zn(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function pf(e){return e.code==="P2009"||e.code==="P2012"}function Vc(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(Vc)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}c();u();p();m();d();l();var $c=Qn;c();u();p();m();d();l();var Hc=Ae(wi());c();u();p();m();d();l();var F=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};k(F,"PrismaClientConstructorValidationError");var qc=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],Bc=["pretty","colorless","minimal"],jc=["info","query","warn","error"],mf={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new F(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=$t(r,t)||` Available datasources: ${t.join(", ")}`;throw new F(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new F(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new F(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new F(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&dt(t.generator)==="client")throw new F('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new F('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!zn(t).includes("driverAdapters"))throw new F('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(dt(t.generator)==="binary")throw new F('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new F(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new F(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!Bc.includes(e)){let t=$t(e,Bc);throw new F(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new F(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!jc.includes(r)){let n=$t(r,jc);throw new F(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=$t(i,o);throw new F(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new F(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new F(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new F(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new F('"omit" option is expected to be an object.');if(e===null)throw new F('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=ff(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let f=o.fields.find(w=>w.name===s);if(!f){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(f.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new F(gf(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new F(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=$t(r,t);throw new F(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function Gc(e,t){for(let[r,n]of Object.entries(e)){if(!qc.includes(r)){let i=$t(r,qc);throw new F(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}mf[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new F('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function $t(e,t){if(t.length===0||typeof e!="string")return"";let r=df(e,t);return r?` Did you mean "${r}"?`:""}function df(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,Hc.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function ff(e,t){return Qc(t.models,e)??Qc(t.types,e)}function Qc(e,t){let r=Object.keys(e).find(n=>$e(n)===t);if(r)return e[r]}function gf(e,t){let r=Tt(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=hn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}c();u();p();m();d();l();function Wc(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},f=w=>{o||(o=!0,r(w))};for(let w=0;w<e.length;w++)e[w].then(v=>{n[w]=v,a()},v=>{if(!Zn(v)){f(v);return}v.batchRequestIdx===w?f(v):(i||(i=v),a())})})}var Ge=J("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var yf={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},hf=Symbol.for("prisma.client.transaction.id"),wf={id:0,nextId(){return++this.id}};function bf(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_middlewares=new Yn;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=Co();constructor(n){e=n?.__internal?.configOverride?.(e)??e,Oa(e),n&&Gc(n,e);let i=new Tn().on("error",()=>{});this._extensions=vt.empty(),this._previewFeatures=zn(e),this._clientVersion=e.clientVersion??$c,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=_c();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&Zr.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&Zr.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let f=e.activeProvider==="postgresql"||e.activeProvider==="cockroachdb"?"postgres":e.activeProvider;if(s.provider!==f)throw new U(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${f}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new U("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=e.injectableEdgeEnv?.();try{let f=n??{},w=f.__internal??{},v=w.debug===!0;v&&J.enable("prisma:client");let A=Zr.resolve(e.dirname,e.relativePath);as.existsSync(A)||(A=e.dirname),Ge("dirname",e.dirname),Ge("relativePath",e.relativePath),Ge("cwd",A);let R=w.engine||{};if(f.errorFormat?this._errorFormat=f.errorFormat:g.env.NODE_ENV==="production"?this._errorFormat="minimal":g.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:A,dirname:e.dirname,enableDebugLogs:v,allowTriggerPanic:R.allowTriggerPanic,prismaPath:R.binaryPath??void 0,engineEndpoint:R.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:f.log&&Lc(f.log),logQueries:f.log&&!!(typeof f.log=="string"?f.log==="query":f.log.find(C=>typeof C=="string"?C==="query":C.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:Da(f,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:f.transactionOptions?.maxWait??2e3,timeout:f.transactionOptions?.timeout??5e3,isolationLevel:f.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:_t,getBatchRequestPayload:Ct,prismaGraphQLToJSError:Cn,PrismaClientUnknownRequestError:se,PrismaClientInitializationError:U,PrismaClientKnownRequestError:Z,debug:J("prisma:client:accelerateEngine"),engineVersion:Kc.version,clientVersion:e.clientVersion}},Ge("clientVersion",e.clientVersion),this._engine=Pc(e,this._engineConfig),this._requestHandler=new ei(this,i),f.log)for(let C of f.log){let D=typeof C=="string"?C:C.emit==="stdout"?C.level:null;D&&this.$on(D,I=>{Jt.log(`${Jt.tags[D]??""}`,I.message||I.query)})}}catch(f){throw f.clientVersion=this._clientVersion,f}return this._appliedParent=ur(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{os()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:Ao({clientMethod:i,activeProvider:a}),callsite:Be(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=Jc(n,i);return vo(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new oe("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(vo(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new oe(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:Tc,callsite:Be(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:Ao({clientMethod:i,activeProvider:a}),callsite:Be(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...Jc(n,i));throw new oe("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new oe("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=wf.nextId(),s=Mc(n.length),a=n.map((f,w)=>{if(f?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let v=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,A={kind:"batch",id:o,index:w,isolationLevel:v,lock:s};return f.requestTransaction?.(A)??f});return Wc(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),f;try{let w={kind:"itx",...a};f=await n(this._createItxClient(w)),await this._engine.transaction("commit",o,a)}catch(w){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),w}return f}_createItxClient(n){return Pe(ur(Pe(ya(this),[ae("_appliedParent",()=>this._appliedParent._createItxClient(n)),ae("_createPrismaPromise",()=>Co(n)),ae(hf,()=>n.id)])),[At(xa)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??yf,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=-1,f=async w=>{let v=this._middlewares.get(++a);if(v)return this._tracingHelper.runInChildSpan(s.middleware,M=>v(w,be=>(M?.end(),f(be))));let{runInTransaction:A,args:R,...C}=w,D={...n,...C};R&&(D.args=i.middlewareArgsToRequestArgs(R)),n.transaction!==void 0&&A===!1&&delete D.transaction;let I=await Aa(this,D);return D.model?Ea({result:I,modelName:D.model,args:D.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):I};return this._tracingHelper.runInChildSpan(s.operation,()=>f(o))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:f,argsMapper:w,transaction:v,unpacker:A,otelParentCtx:R,customDataProxyFetch:C}){try{n=w?w(n):n;let D={name:"serialize"},I=this._tracingHelper.runInChildSpan(D,()=>Ci({modelName:f,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return J.enabled("prisma:client")&&(Ge("Prisma Client call:"),Ge(`prisma.${i}(${sa(n)})`),Ge("Generated request:"),Ge(JSON.stringify(I,null,2)+`
`)),v?.kind==="batch"&&await v.lock,this._requestHandler.request({protocolQuery:I,modelName:f,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:v,unpacker:A,otelParentCtx:R,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:C})}catch(D){throw D.clientVersion=this._clientVersion,D}}$metrics=new ar(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=ha}return t}function Jc(e,t){return Ef(e)?[new ye(e,t),kc]:[e,Oc]}function Ef(e){return Array.isArray(e)&&Array.isArray(e.raw)}c();u();p();m();d();l();var xf=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Pf(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!xf.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}c();u();p();m();d();l();l();var export_warnEnvConflicts=void 0;export{ln as DMMF,J as Debug,ie as Decimal,Qo as Extensions,ar as MetricsClient,U as PrismaClientInitializationError,Z as PrismaClientKnownRequestError,pe as PrismaClientRustPanicError,se as PrismaClientUnknownRequestError,oe as PrismaClientValidationError,Go as Public,ye as Sql,Op as createParam,qp as defineDmmfProperty,Ze as deserializeJsonResponse,ko as deserializeRawResult,zu as dmmfToRuntimeDataModel,Hp as empty,bf as getPrismaClient,mr as getRuntime,Qp as join,Pf as makeStrictEnum,jp as makeTypedQueryFactory,xi as objectEnumValues,ra as raw,Ci as serializeJsonQuery,vi as skip,na as sqltag,export_warnEnvConflicts as warnEnvConflicts,sn as warnOnce};
//# sourceMappingURL=wasm-compiler-edge.mjs.map
