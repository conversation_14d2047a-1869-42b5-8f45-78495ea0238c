{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/controllers/*": ["src/controllers/*"], "@/routes/*": ["src/routes/*"], "@/middleware/*": ["src/middleware/*"], "@/models/*": ["src/models/*"], "@/utils/*": ["src/utils/*"], "@/config/*": ["src/config/*"], "@/types/*": ["src/types/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*", "src/**/*.json"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}