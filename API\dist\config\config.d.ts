interface Config {
    port: number;
    nodeEnv: string;
    frontendUrl: string;
    databaseUrl: string;
    jwtSecret: string;
    jwtExpiresIn: string;
    jwtRefreshSecret: string;
    jwtRefreshExpiresIn: string;
    bcryptSaltRounds: number;
    smtp: {
        host: string;
        port: number;
        user: string;
        pass: string;
        fromEmail: string;
        fromName: string;
    };
    upload: {
        maxFileSize: number;
        uploadPath: string;
    };
    rateLimit: {
        windowMs: number;
        maxRequests: number;
    };
    cors: {
        allowedOrigins: string[];
    };
}
declare const config: Config;
export { config };
//# sourceMappingURL=config.d.ts.map